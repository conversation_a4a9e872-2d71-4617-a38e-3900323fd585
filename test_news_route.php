<?php
// 测试新闻路由脚本
echo "=== 新闻路由测试 ===\n";

// 检查关键文件
$files_to_check = [
    'application/web/controller/News.php',
    'application/web/view/news/detail.php',
    'application/route.php',
    'public/.htaccess'
];

echo "检查关键文件:\n";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ $file 存在\n";
    } else {
        echo "✗ $file 不存在\n";
    }
}

// 检查News控制器的detail方法
if (file_exists('application/web/controller/News.php')) {
    $content = file_get_contents('application/web/controller/News.php');
    if (strpos($content, 'function detail()') !== false) {
        echo "✓ News控制器包含detail方法\n";
    } else {
        echo "✗ News控制器缺少detail方法\n";
    }
}

echo "\n建议的访问URL:\n";
echo "1. http://localhost/web/news/detail/id/513.html\n";
echo "2. http://localhost/web/news/detail/id/513\n";
echo "3. http://localhost/index.php/web/news/detail/id/513\n";
echo "4. http://localhost/index.php?s=web/news/detail&id=513\n";

echo "\n如果仍然无法访问，请尝试:\n";
echo "1. 清除路由缓存: 删除 runtime/cache 目录下的文件\n";
echo "2. 检查web服务器是否正在运行\n";
echo "3. 确认DocumentRoot指向public目录\n";
echo "4. 检查URL重写模块是否启用\n";

// 检查数据库连接（如果需要）
echo "\n检查数据库配置:\n";
if (file_exists('application/database.php')) {
    echo "✓ 数据库配置文件存在\n";
} else {
    echo "✗ 数据库配置文件不存在\n";
}
?>
