/* headerBox */
.headerBox {
  width: 100%;
  padding: 15px 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  background-color: #1d2b57;
  transition: all 0.6s ease;
}
.headerBox .logo {
  display: block;
  width: 156px;
  height: 70px;
  background: url('../images/logo.png') center center no-repeat;
  background-size: cover;
}
.headerBox .box {
  width: calc(100% - 156px);
}
.headerBox .navBox .nLi {
  margin-left: 70px;
  position: relative;
}
.headerBox .navBox .nLi h3 a {
  display: block;
  line-height: 40px;
  color: #fff;
  font-size: 18px;
  padding: 15px 0;
  font-weight: normal;
}
.headerBox .navBox .nLi .sub {
  display: none;
  width: 130px;
  position: absolute;
  top: 70px;
  transform: translateX(-50%);
  left: 50%;
  z-index: 3;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
}
.headerBox .navBox .nLi .sub ul {
  width: 100%;
  padding: 10px 0;
  background-color: #fff;
  border-radius: 2px;
  position: relative;
}
.headerBox .navBox .nLi .sub ul a {
  display: block;
  text-align: center;
  font-size: 14px;
  line-height: 20px;
  color: #333;
  padding: 10px 8px;
  transition: all 0.6s ease;
}
.headerBox .navBox .nLi .sub ul li:hover a {
  color: #082CA4;
}
.headerBox .language {
  color: #fff;
  line-height: 70px;
  font-size: 18px;
  margin: 0 70px;
}
.headerBox .login {
  width: 130px;
  height: 42px;
  border-radius: 2px;
  border: 1px solid rgba(255, 255, 255, 0.6);
  font-size: 16px;
  line-height: 42px;
  color: #ffffff;
  margin-top: 14px;
  transition: all 0.6s ease;
}
.headerBox .login .iconfont {
  font-size: 18px;
  margin-right: 10px;
}
.headerBox .login.login1 {
  width: 130px;
  font-size: 14px;
}
.headerBox .login.login1 .iconfont {
  font-size: 16px;
}
.headerBox .login:hover {
  background-color: #fff;
  color: #082CA4;
}
.headerBox.headerBox1 {
  background-color: #fff;
}
.headerBox.headerBox1 .logo {
  background: url('../images/logo1.png') center center no-repeat;
  background-size: cover;
}
.headerBox.headerBox1 .navBox .nLi h3 a {
  color: #333;
}
.headerBox.headerBox1 .navBox .nLi:hover h3 a,
.headerBox.headerBox1 .navBox .nLi.on h3 a {
  color: #082CA4;
}
.headerBox.headerBox1 .language {
  color: #333;
}
.headerBox.headerBox1 .login {
  border: 1px solid #082CA4;
  background-color: #082CA4;
}
.headerBox.headerBox1 .login:hover {
  background-color: #fff;
  color: #082CA4;
}
.is-fixed {
  padding: 10px 0;
  background-color: rgba(29, 43, 87, 0.8);
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
}
@media (max-width: 1680px) {
  .headerBox .navBox .nLi h3 a {
    font-size: 16px;
  }
  .headerBox .language {
    font-size: 16px;
    margin: 0 40px;
  }
  .headerBox .login {
    width: 110px;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    margin-top: 15px;
  }
  .headerBox .login .iconfont {
    font-size: 16px;
  }
}
@media (max-width: 1560px) {
  .headerBox .navBox .nLi {
    margin-left: 60px;
  }
}
@media (max-width: 1480px) {
  .headerBox .logo {
    width: 124px;
    height: 56px;
    margin-top: 7px;
  }
  .headerBox .box {
    width: calc(100% - 124px);
  }
  .headerBox .navBox .nLi {
    margin-left: 48px;
  }
  .headerBox .language {
    margin: 0 50px;
  }
  .headerBox .login {
    width: 100px;
  }
  .headerBox .login .iconfont {
    margin-right: 6px;
  }
  .headerBox .login.login1 {
    width: 110px;
    font-size: 12px;
  }
  .headerBox .login.login1 .iconfont {
    font-size: 14px;
  }
}
@media (max-width: 1366px) {
  .headerBox .navBox .nLi {
    margin-left: 40px;
  }
}
/* indexBanner */
.indexBanner {
  width: 100%;
  overflow: hidden;
  margin-top: 50px;
}
.indexBanner img {
  width: 100%;
  display: block;
}
.indexBanner .swiper-button-next:after,
.indexBanner .swiper-button-prev:after {
  color: #fff;
  opacity: 0;
}
/* indexTitle */
.indexTitle {
  width: 100%;
  align-items: center;
}
.indexTitle .title {
  font-size: 40px;
  line-height: 44px;
  color: #333333;
  font-weight: bold;
}
.indexTitle ul li {
  font-size: 20px;
  font-weight: 300;
  line-height: 24px;
  color: rgba(51, 51, 51, 0.8);
  padding-bottom: 14px;
  margin-left: 50px;
  cursor: pointer;
}
.indexTitle ul li.on {
  font-weight: bold;
  color: #082CA4;
  border-bottom: 2px solid #082CA4;
}
.indexTitle .more {
  font-size: 16px;
  line-height: 44px;
  color: #333333;
}
.indexTitle .more .iconfont {
  font-size: 18px;
  color: #082CA4;
  transform: rotate(180deg);
  margin-left: 10px;
}
@media (max-width: 1680px) {
  .indexTitle .title {
    font-size: 32px;
    line-height: 36px;
  }
  .indexTitle ul li {
    font-size: 18px;
    line-height: 22px;
  }
  .indexTitle .more {
    line-height: 36px;
  }
}
@media (max-width: 1480px) {
  .indexTitle .title {
    font-size: 30px;
    line-height: 34px;
  }
  .indexTitle ul li {
    font-size: 16px;
    line-height: 20px;
  }
  .indexTitle .more {
    line-height: 34px;
  }
}
/* indexSchedule */
.indexSchedule {
  width: 100%;
  background: #fff url(../images/bg.png) top -600px right -770px no-repeat;
  background-size: 1209px auto;
  padding: 100px 0 70px;
  overflow: hidden;
}
.indexSchedule .timeBox {
  width: 100%;
  margin-top: 50px;
}
.indexSchedule .timeBox li {
  width: 15%;
  height: 56px;
  border-radius: 28px;
  border: 1px solid #082CA4;
  font-size: 20px;
  line-height: 56px;
  color: #082CA4;
  text-align: center;
  cursor: pointer;
  margin: 0 4%;
  transition: all 0.6s ease;
}
.indexSchedule .timeBox li.on,
.indexSchedule .timeBox li:hover {
  background-color: #082CA4;
  color: #fff;
}
.scheduleBox {
  width: 100%;
  margin-top: 40px;
}
.scheduleBox .liBox {
  width: 100%;
  margin-bottom: 20px;
}
.scheduleBox .Box {
  width: 100%;
  min-height: 110px;
  background: linear-gradient(180deg, #EEF2FF 0%, #FFFFFF 100%);
  border: 2px solid rgba(8, 44, 164, 0.08);
  align-items: center;
  padding: 1% 30px;
  transition: all 0.6s ease;
}
.scheduleBox .Box .titBox {
  width: calc(100% - 200px);
}
.scheduleBox .Box .titBox .tit {
  max-width: calc(100% - 130px);
  font-size: 18px;
  font-weight: bold;
  line-height: 26px;
  color: #333333;
}
.scheduleBox .Box .titBox .label {
  min-width: 78px;
  height: 28px;
  line-height: 28px;
  border-radius: 2px;
  font-size: 14px;
  margin-left: 20px;
  padding: 0 4px;
}
.scheduleBox .Box .titBox .label.label1 {
  border: 1px solid #52A199;
  color: #52A199;
}
.scheduleBox .Box .titBox .label.label2 {
  border: 1px solid #082CA4;
  color: #082CA4;
}
.scheduleBox .Box .titBox .label.label3 {
  border: 1px solid #999999;
  color: #999999;
}
.scheduleBox .Box .titBox .label .iconfont {
  margin-right: 3px;
  font-size: 16px;
}
.scheduleBox .Box .titBox .address {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 18px;
}
.scheduleBox .Box .titBox .address .iconfont {
  font-size: 18px;
  margin-right: 5px;
}
.scheduleBox .Box .btnBox {
  align-items: center;
}
.scheduleBox .Box .btnBox .btn {
  display: block;
  width: 130px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  font-size: 16px;
  color: #FFFFFF;
  text-align: center;
}
.scheduleBox .Box .btnBox .btn.btn1 {
  background-color: #082CA4;
}
.scheduleBox .Box .btnBox .btn.btn2 {
  background-color: #CDCFD6;
}
.scheduleBox .Box .btnBox .iconfont {
  width: 40px;
  height: 40px;
  line-height: 40px;
  color: #fff;
  font-size: 12px;
  margin-left: 16px;
  background-color: #262626;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.6s ease;
}
.scheduleBox .Box.on {
  background: url(../images/titBg.jpg) center center no-repeat;
  background-size: cover;
}
.scheduleBox .Box.on .titBox .tit {
  color: #fff;
}
.scheduleBox .Box.on .titBox .label.label1 {
  border: 1px solid #fff;
  color: #fff;
}
.scheduleBox .Box.on .titBox .label.label2 {
  border: 1px solid #fff;
  color: #fff;
}
.scheduleBox .Box.on .titBox .label.label3 {
  border: 1px solid #fff;
  color: #fff;
}
.scheduleBox .Box.on .titBox .address {
  color: #fff;
}
.scheduleBox .Box.on .btnBox .btn.btn1 {
  color: #082CA4;
  background-color: #fff;
}
.scheduleBox .Box.on .btnBox .btn.btn2 {
  color: #CDCFD6;
  background-color: #fff;
}
.scheduleBox .Box.on .btnBox .iconfont {
  background-color: #fff;
  color: #333;
  transform: rotate(90deg);
}
.scheduleBox .contBox {
  width: 100%;
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-top: none;
  display: none;
}
.scheduleBox .contBox.sel {
  display: block;
}
.scheduleBox .contBox .cont {
  width: 100%;
  background-color: #FAFBFF;
  padding: 1% 30px 26px;
}
.scheduleBox .contBox .cont .t {
  font-size: 16px;
  line-height: 24px;
  color: #3D3D3D;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 12px 0;
}
.scheduleBox .contBox .cont .t p {
  margin: 6px 0;
}
.scheduleBox .contBox .cont .t p:nth-child(1) {
  min-width: 80px;
}
.scheduleBox .contBox .cont .t p:nth-child(2) {
  min-width: calc(100% - 80px);
}
.scheduleBox .contBox .cont .t p span:last-child em {
  display: none;
}
.scheduleBox .contBox .cont .t1 {
  font-size: 16px;
  line-height: 20px;
  color: #3D3D3D;
  padding: 20px 0 14px;
}
.scheduleBox .contBox .cont .t2 {
  font-size: 14px;
  line-height: 28px;
  color: #666666;
  overflow: hidden;
}
.scheduleBox .contBox .cont .t2.on {
  height: auto;
}
.scheduleBox .contBox .cont .show {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  line-height: 18px;
  color: #333333;
  cursor: pointer;
}
.scheduleBox .contBox .cont .show p {
  margin-bottom: 5px;
}
.scheduleBox .contBox .cont .show .iconfont {
  width: 36px;
  height: 36px;
  line-height: 36px;
  border-radius: 18px;
  background-color: #262626;
  color: #fff;
  transform: rotate(90deg);
  margin: 0 auto;
  text-align: center;
  font-size: 16px;
}
.scheduleBox .contBox .dl {
  width: 100%;
  padding: 50px 30px 20px;
  position: relative;
}
.scheduleBox .contBox .dl::after {
  content: '';
  width: 1px;
  height: calc(100% - 156px);
  border-left: 1px dashed #082CA4;
  position: absolute;
  top: 79px;
  left: 190px;
}
.scheduleBox .contBox .dl .dt {
  width: 100%;
  margin-bottom: 30px;
}
.scheduleBox .contBox .dl .dt .time {
  width: 170px;
  font-size: 16px;
  line-height: 22px;
  color: #333333;
  margin-right: 60px;
  padding-right: 70px;
  text-align: right;
  position: relative;
  padding-top: 20px;
}
.scheduleBox .contBox .dl .dt .time::after {
  content: '';
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: rgba(8, 44, 164, 0.1);
  position: absolute;
  top: 23px;
  right: 2px;
}
.scheduleBox .contBox .dl .dt .time::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #082CA4;
  position: absolute;
  top: 27px;
  right: 6px;
}
.scheduleBox .contBox .dl .dt .dd {
  width: calc(100% - 230px);
  background-color: #FAFBFF;
  padding: 20px 2% 0;
}
.scheduleBox .contBox .dl .dt .dd .t3 {
  font-size: 16px;
  line-height: 22px;
  color: #333333;
  margin-bottom: 20px;
  font-weight: bold;
}
.scheduleBox .contBox .dl .dt .dd .li {
  width: 31.3333%;
  margin: 0 3% 30px 0;
  align-items: center;
}
.scheduleBox .contBox .dl .dt .dd .li:nth-child(3n) {
  margin: 0 0 30px 0;
}
.scheduleBox .contBox .dl .dt .dd .li .img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}
.scheduleBox .contBox .dl .dt .dd .li .img img {
  width: 100%;
  display: block;
}
.scheduleBox .contBox .dl .dt .dd .li .t4 {
  width: calc(100% - 60px);
}
.scheduleBox .contBox .dl .dt .dd .li .t4 .name {
  font-size: 14px;
  line-height: 18px;
  color: #333333;
}
.scheduleBox .contBox .dl .dt .dd .li .t4 .name span {
  font-size: 12px;
  line-height: 16px;
  color: #999;
}
.scheduleBox .contBox .dl .dt .dd .li .t4 .t5 {
  font-size: 12px;
  line-height: 18px;
  color: #999;
  margin-top: 4px;
}
@media (max-width: 1480px) {
  .indexSchedule .timeBox li {
    height: 46px;
    line-height: 46px;
    border-radius: 23px;
    font-size: 16px;
  }
  .scheduleBox .Box .titBox {
    width: calc(100% - 190px);
  }
  .scheduleBox .Box .titBox .tit {
    max-width: calc(100% - 110px);
    font-size: 16px;
    line-height: 24px;
  }
  .scheduleBox .Box .titBox .label {
    min-width: 70px;
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }
  .scheduleBox .Box .titBox .label .iconfont {
    font-size: 14px;
  }
  .scheduleBox .Box .titBox .address {
    font-size: 14px;
    line-height: 18px;
  }
  .scheduleBox .Box .titBox .address .iconfont {
    font-size: 16px;
  }
  .scheduleBox .Box .btnBox .btn {
    width: 120px;
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
    font-size: 14px;
  }
  .scheduleBox .Box .btnBox .iconfont {
    width: 36px;
    height: 36px;
    line-height: 36px;
  }
}
/* indexNews */
.indexNews {
  width: 100%;
  overflow: hidden;
  padding: 120px 0;
  background: url(../images/newsBg.jpg) center center no-repeat;
  background-size: cover;
}
.indexNews .box {
  width: 100%;
  margin-top: 50px;
}
.indexNews .contBox {
  display: block;
  width: 32%;
  /* background-color: #fff; */
}
.indexNews .contBox .img {
  width: 100%;
  height: 322px;
  overflow: hidden;
}
.indexNews .contBox .img img {
  width: 100%;
  height: 100%;
  display: block;
  transition: all 0.6s ease;
}
.indexNews .contBox .cont {
  padding: 6%;
}
.indexNews .contBox .cont .time {
  display: inline-block;
  height: 36px;
  line-height: 36px;
  border-radius: 2px;
  background-color: #082CA4;
  padding: 0 12px;
  font-size: 18px;
  color: #FFFFFF;
}
.indexNews .contBox .cont .tit {
  font-size: 20px;
  line-height: 24px;
  color: #333333;
  margin-top: 20px;
  transition: all 0.6s ease;
}
.indexNews .contBox .cont .txt {
  font-size: 16px;
  line-height: 24px;
  color: #666666;
  margin-top: 16px;
  height: 48px;
}
.indexNews .contBox:hover .img img {
  transform: scale(1.1);
}
.indexNews .contBox:hover .cont .tit {
  color: #082CA4;
}
.indexNews .newsBox {
  width: 32%;
  background: url(../images/newsImgBg.jpg) center center no-repeat;
  background-size: cover;
}
.indexNews .newsBox a {
  display: block;
  width: 100%;
  height: 100%;
  padding: 13% 9%;
}
.indexNews .newsBox a .time {
  display: inline-block;
  height: 36px;
  line-height: 36px;
  border-radius: 2px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 12px;
  font-size: 14px;
  color: #FFFFFF;
}
.indexNews .newsBox a .tit {
  font-size: 20px;
  line-height: 36px;
  color: #FFFFFF;
  margin-top: 20px;
}
.indexNews .newsBox a .txt {
  font-size: 16px;
  line-height: 36px;
  color: #FFFFFF;
  margin-top: 20px;
}
.indexNews .newsBox .swiper-pagination {
  padding: 0 9%;
  bottom: 10%;
  display: flex;
  justify-content: flex-start;
}
.indexNews .newsBox .swiper-pagination .swiper-pagination-bullet {
  opacity: 0.5;
  background-color: #fff;
  margin: 0 15px 0 0;
}
.indexNews .newsBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
}
.indexNews ul {
  width: 32%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.indexNews ul li {
    width: 100%;
    margin-top: 9px;
    margin-bottom: 20px;
    align-items: center;
}
.indexNews ul li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.indexNews ul li a {
  align-items: center;
  text-decoration: none;
  color: inherit;
  padding-left: 0;
  padding-right: 15px;
}
.indexNews ul li:first-child a{
  padding-top: 0;
}
/* 新的新闻项目样式 */
.news-item {
    display: block;
    width: 100%;
    position: relative;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}
.news-header {
    margin-bottom: 12px;
}

.news-title {
    font-size: 18px;
    color: #333;
    line-height: 1.4;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}

.news-body {
    font-size: 16px;
    color: #666;
    line-height: 1.8;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}

.news-date {
    font-size: 16px;
    color: #999;
    text-align: left;
    margin-bottom: 15px;
}

.indexNews ul li .img {
    width: 49%;
    height: 158px;
    overflow: hidden;
}
.indexNews ul li .img img {
  width: 100%;
  height: 100%;
  display: block;
  transition: all 0.6s ease;
}
.indexNews ul li .cont {
  width: 45%;
}
.indexNews ul li .cont .tit {
  font-size: 18px;
  line-height: 22px;
  color: #333333;
  transition: all 0.6s ease;
}
.indexNews ul li .cont .txt {
  font-size: 14px;
  line-height: 24px;
  color: #666666;
  margin-top: 14px;
}
.indexNews ul li .cont .time {
  font-size: 14px;
  line-height: 18px;
  color: #999999;
  margin-top: 14px;
}
.indexNews ul li:hover .img img {
  transform: scale(1.1);
}
.indexNews ul li:hover .cont .tit {
  color: #082CA4;
}
@media (max-width: 1680px) {
  .indexNews .contBox .img {
    height: 268px;
  }
  .indexNews ul li {
    margin-bottom: 18px;
  }
  .news-item {
    padding: 18px;
  }
  .news-title {
    font-size: 18px;
  }
  .news-body {
    font-size: 16px;
  }
}
@media (max-width: 1480px) {
  .indexNews .contBox .img {
    height: 246px;
  }
  .indexNews .contBox .cont .time {
    height: 32px;
    line-height: 32px;
  }
  .indexNews .contBox .cont .tit {
    font-size: 18px;
    line-height: 22px;
  }
  .indexNews .contBox .cont .txt {
    font-size: 14px;
    line-height: 28px;
  }
  .indexNews .newsBox a .time {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
  }
  .indexNews .newsBox a .tit {
    font-size: 18px;
    line-height: 30px;
  }
  .indexNews .newsBox a .txt {
    font-size: 14px;
    line-height: 28px;
  }
  .indexNews ul li {
    margin-bottom: 16px;
  }
  .news-item {
    padding: 16px;
  }
  .news-title {
    font-size: 18px;
  }
  .news-body {
    font-size: 16px;
  }
  .news-date {
    font-size: 11px;
  }
}
/* indexReport */
.indexReport {
  width: 100%;
  padding: 126px 0;
  background: url(../images/reportBg.jpg) center center no-repeat;
  background-size: cover;
}
.indexReport .tabList {
  width: 100%;
  position: relative;
}
.indexReport .tabList li {
  padding-bottom: 12px;
  margin-right: 40px;
  cursor: pointer;
}
.indexReport .tabList li .p {
  font-size: 40px;
  font-weight: 300;
  line-height: 44px;
  color: rgba(255, 255, 255, 0.8);
}
.indexReport .tabList li .more {
  font-size: 16px;
  line-height: 30px;
  color: #fff;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  display: none;
}
.indexReport .tabList li .more .iconfont {
  font-size: 18px;
  color: #fff;
  transform: rotate(180deg);
  margin-left: 10px;
}
.indexReport .tabList li.on {
  border-bottom: 2px solid #fff;
}
.indexReport .tabList li.on .p {
  font-weight: bold;
  color: #FFFFFF;
}
.indexReport .tabList li.on .more {
  display: block;
}
.indexReport .lists {
  width: 100%;
  margin-top: 36px;
  position: relative;
}
.indexReport .reportBox {
  width: 100%;
  padding: 0 calc((100% - 1440px) / 2) 110px;
}
.indexReport .reportBox .swiper-slide a {
  display: block;
  width: 100%;
  height: 340px;
  background-color: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  padding: 11% 7% 5%;
  transition: all 0.6s ease;
}
.indexReport .reportBox .swiper-slide a .label {
  display: inline-block;
  height: 34px;
  line-height: 34px;
  border-radius: 2px;
  background-color: #082CA4;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0 14px;
  transition: all 0.6s ease;
}
.indexReport .reportBox .swiper-slide a .tit {
  font-size: 20px;
  line-height: 32px;
  color: #FFFFFF;
  margin-top: 30px;
}
.indexReport .reportBox .swiper-slide a .txt {
  font-size: 16px;
  line-height: 30px;
  color: #FFFFFF;
  margin-top: 16px;
}
.indexReport .reportBox .swiper-slide a .more {
  font-size: 16px;
  line-height: 18px;
  color: #FFFFFF;
  margin-top: 40px;
}
.indexReport .reportBox .swiper-slide a .more .iconfont {
  width: 18px;
  height: 18px;
  line-height: 18px;
  border-radius: 50%;
  background-color: #fff;
  color: #333;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  margin-left: 12px;
}
.indexReport .reportBox .swiper-slide a .down {
  width: 150px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  background-color: #082CA4;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
  margin-top: 50px;
  transition: all 0.6s ease;
}
.indexReport .reportBox .swiper-slide a:hover {
  background-color: #082CA4;
  border: 2px solid #082CA4;
}
.indexReport .reportBox .swiper-slide a:hover .label {
  background-color: #fff;
  color: #082CA4;
}
.indexReport .reportBox .swiper-slide a:hover .down {
  background-color: #fff;
  color: #082CA4;
}
.indexReport .reportBox .arrow {
  width: 156px;
  height: 48px;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.indexReport .reportBox .arrow .swiper-button-next,
.indexReport .reportBox .arrow .swiper-button-prev {
  width: 48px;
  height: 48px;
  background-color: #fff;
  border-radius: 50%;
  top: 0;
  margin-top: 0;
  transition: all 0.6s ease;
}
.indexReport .reportBox .arrow .swiper-button-next::after,
.indexReport .reportBox .arrow .swiper-button-prev::after {
  color: #000;
  font-size: 14px;
}
.indexReport .reportBox .arrow .swiper-button-next:hover,
.indexReport .reportBox .arrow .swiper-button-prev:hover {
  background-color: #082CA4;
}
.indexReport .reportBox .arrow .swiper-button-next:hover::after,
.indexReport .reportBox .arrow .swiper-button-prev:hover::after {
  color: #fff;
}
@media (max-width: 1680px) {
  .indexReport .tabList li .p {
    font-size: 32px;
    line-height: 36px;
  }
  .indexReport .reportBox {
    padding: 0 calc((100% - 1200px) / 2) 110px;
  }
}
@media (max-width: 1480px) {
  .indexReport .tabList li .p {
    font-size: 30px;
    line-height: 34px;
  }
  .indexReport .reportBox {
    width: 100%;
    padding: 0 calc((100% - 1100px) / 2) 110px;
  }
  .indexReport .reportBox .swiper-slide a {
    height: 310px;
  }
  .indexReport .reportBox .swiper-slide a .label {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }
  .indexReport .reportBox .swiper-slide a .tit {
    font-size: 18px;
    line-height: 30px;
  }
  .indexReport .reportBox .swiper-slide a .txt {
    font-size: 14px;
    line-height: 28px;
  }
  .indexReport .reportBox .swiper-slide a .more {
    font-size: 14px;
    line-height: 16px;
  }
  .indexReport .reportBox .swiper-slide a .more .iconfont {
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-size: 8px;
    margin-left: 10px;
  }
  .indexReport .reportBox .swiper-slide a .down {
    width: 130px;
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
    font-size: 12px;
  }
  .indexReport .reportBox .arrow {
    width: 136px;
    height: 40px;
  }
  .indexReport .reportBox .arrow .swiper-button-next,
  .indexReport .reportBox .arrow .swiper-button-prev {
    width: 40px;
    height: 40px;
  }
  .indexReport .reportBox .arrow .swiper-button-next::after,
  .indexReport .reportBox .arrow .swiper-button-prev::after {
    font-size: 12px;
  }
}
/* indexActivity */
.indexActivity {
  width: 100%;
  padding: 100px 0 140px;
  background: #efefef url(../images/activityBg.png) center center no-repeat;
  background-size: cover;
}
.indexActivity .indexTitle .title{
  font-size: 48px;
}
.indexActivity .activityBox {
  width: 100%;
  margin-top: 50px;
  padding-bottom: 60px;
}
.indexActivity .activityBox .swiper-slide {
  width: 560px;
}

.indexActivity .activityBox .swiper-slide a {
  width: 100%;
}
.indexActivity .activityBox .swiper-slide a .img {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.indexActivity .activityBox .swiper-slide a .img img {
  width: 100%;
  height: 100%;
  display: block;
}
.indexActivity .activityBox .swiper-slide a .cont {
  width: 100%;
  height: 110px;
  /* background: url(../images/activityImgBg.jpg) center center no-repeat; */
  background-color: #fff;
  background-size: cover;
  padding: 20px;
}
.indexActivity .activityBox .swiper-slide a .cont .label {
  display: inline-block;
  height: 34px;
  line-height: 34px;
  border-radius: 2px;
  background-color: #082CA4;
  /* border: 1px solid #082CA4; */
  padding: 0 14px;
  font-size: 14px;
  color: #fff;
}
.indexActivity .activityBox .swiper-slide a .cont .tit {
  font-size: 18px;
  line-height: 32px;
  color: #333;
  margin-top: 5px;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}
.indexActivity .activityBox .swiper-slide a .cont .txt {
  font-size: 18px;
  line-height: 30px;
  color: #FFFFFF;
  margin-top: 10px;
  
}
.indexActivity .activityBox .swiper-slide a .cont .t {
  font-size: 16px;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 16px;
}



.indexActivity .activityBox .swiper-slide.swiper-slide-active a .cont {
  background-color: #082CA4;
}
.indexActivity .activityBox .swiper-slide.swiper-slide-active a .cont .label{
  background-color: #fff;
  color: #082CA4;
}
.indexActivity .activityBox .swiper-slide.swiper-slide-active a .cont .tit{
  color: #fff;
}

.indexActivity .activityBox .swiper-slide a .cont .t .iconfont {
  font-size: 18px;
  margin-right: 10px;
}
.indexActivity .activityBox .swiper-pagination {
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.indexActivity .activityBox .swiper-pagination .swiper-pagination-bullet {
  width: 14px;
  height: 14px;
  opacity: 0.4;
  background-color: #082CA4;
  margin: 0 10px;
}
.indexActivity .activityBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 18px;
  height: 18px;
  opacity: 1;
}
@media (max-width: 1680px) {
  .indexActivity .activityBox .swiper-slide {
    width: 560px;
  }
  .indexActivity .activityBox .swiper-slide a .img {
    width: 100%;
  }
  .indexActivity .activityBox .swiper-slide a .cont {
    width: 100%;
    height: 110px;
  }
  .indexActivity .activityBox .swiper-slide a .cont .txt {
    margin-top: 30px;
  }
  .indexActivity .activityBox .swiper-pagination .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
  }
  .indexActivity .activityBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    width: 14px;
    height: 14px;
  }
}
@media (max-width: 1480px) {
  .indexActivity .activityBox .swiper-slide {
    width: 960px;
  }
  .indexActivity .activityBox .swiper-slide a .img {
    width: 100%;
  }
  .indexActivity .activityBox .swiper-slide a .cont {
    height: 110px;
    width: 100%;
  }
  .indexActivity .activityBox .swiper-slide a .cont .label {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }
  .indexActivity .activityBox .swiper-slide a .cont .tit {
    font-size: 18px;
    line-height: 30px;
  }
  .indexActivity .activityBox .swiper-slide a .cont .txt {
    font-size: 14px;
    line-height: 28px;
  }
  .indexActivity .activityBox .swiper-slide a .cont .t {
    font-size: 14px;
    line-height: 18px;
  }
  .indexActivity .activityBox .swiper-slide a .cont .t .iconfont {
    font-size: 16px;
    margin-right: 6px;
  }
  .indexActivity .activityBox .swiper-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
  }
  .indexActivity .activityBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    width: 12px;
    height: 12px;
  }
}
/* indexAd */
.indexAd {
  width: 100%;
  padding: 85px 0;
  background: url(../images/adBg.jpg) center center no-repeat;
  background-size: cover;
}
.indexAd .t1 {
  font-size: 20px;
  line-height: 24px;
  color: #FFFFFF;
}
.indexAd .t2 {
  font-size: 40px;
  font-weight: bold;
  line-height: 44px;
  color: #FFFFFF;
  margin-top: 26px;
}
@media (max-width: 1480px) {
  .indexAd .t1 {
    font-size: 18px;
    line-height: 26px;
  }
  .indexAd .t2 {
    font-size: 32px;
    line-height: 36px;
  }
}
/* indexVideo */
.indexVideo {
  width: 100%;
  padding: 100px 0 80px;
  background: url(../images/videoBg.jpg) center center no-repeat;
  background-size: cover;
}
.indexVideo .indexTitle .title {
  color: #fff;
}
.indexVideo .indexTitle ul li {
  color: rgba(255, 255, 255, 0.8);
}
.indexVideo .indexTitle ul li.on {
  color: #fff;
  border-bottom: 2px solid #fff;
}
.indexVideo .lists {
  width: 100%;
  margin-top: 50px;
}
.indexVideo .videoBox {
  width: 100%;
  padding: 0 calc((100% - 1440px) / 2) 130px;
}
.indexVideo .videoBox .swiper-slide a {
  display: block;
  width: 100%;
}
.indexVideo .videoBox .swiper-slide a .img {
  width: 100%;
  height: 486px;
  overflow: hidden;
  position: relative;
}
.indexVideo .videoBox .swiper-slide a .img img {
  width: 100%;
  height: 100%;
  display: block;
  transition: all 0.6s ease;
}
.indexVideo .videoBox .swiper-slide a .img .iconfont {
  position: absolute;
  bottom: 30px;
  right: 30px;
  color: #fff;
  font-size: 40px;
  line-height: 44px;
}
.indexVideo .videoBox .swiper-slide a .cont {
  width: 100%;
  position: relative;
  padding-top: 60px;
}
.indexVideo .videoBox .swiper-slide a .cont .label {
  display: inline-block;
  height: 32px;
  line-height: 32px;
  border-radius: 2px;
  background-color: #082CA4;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0 14px;
  position: absolute;
  top: -18px;
  left: 20px;
}
.indexVideo .videoBox .swiper-slide a .cont .tit {
  font-size: 20px;
  line-height: 24px;
  color: #fff;
  transition: all 0.6s ease;
}
.indexVideo .videoBox .swiper-slide a .cont .txt {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 30px;
}
.indexVideo .videoBox .swiper-slide a:hover .img img {
  transform: scale(1.1);
}
.indexVideo .videoBox .arrowBox {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  align-items: center;
}
.indexVideo .videoBox .arrowBox .arrow {
  width: 130px;
  height: 48px;
  position: relative;
}
.indexVideo .videoBox .arrowBox .arrow .swiper-button-next {
  right: 0;
}
.indexVideo .videoBox .arrowBox .arrow .swiper-button-prev {
  left: 0;
}
.indexVideo .videoBox .arrowBox .arrow .swiper-button-next,
.indexVideo .videoBox .arrowBox .arrow .swiper-button-prev {
  width: 48px;
  height: 48px;
  background-color: #262626;
  border-radius: 50%;
  top: 0;
  margin-top: 0;
  transition: all 0.6s ease;
}
.indexVideo .videoBox .arrowBox .arrow .swiper-button-next::after,
.indexVideo .videoBox .arrowBox .arrow .swiper-button-prev::after {
  color: #fff;
  font-size: 14px;
}
.indexVideo .videoBox .arrowBox .arrow .swiper-button-next:hover,
.indexVideo .videoBox .arrowBox .arrow .swiper-button-prev:hover {
  background-color: #082CA4;
}
.indexVideo .videoBox .arrowBox .more {
  font-size: 16px;
  line-height: 32px;
  color: #fff;
}
.indexVideo .videoBox .arrowBox .more .iconfont {
  font-size: 18px;
  color: #fff;
  transform: rotate(180deg);
  margin-left: 10px;
}
@media (max-width: 1680px) {
  .indexVideo .videoBox {
    padding: 0 calc((100% - 1200px) / 2) 130px;
  }
  .indexVideo .videoBox .swiper-slide a .img {
    height: 402px;
  }
}
@media (max-width: 1480px) {
  .indexVideo .videoBox {
    padding: 0 calc((100% - 1100px) / 2) 130px;
  }
  .indexVideo .videoBox .swiper-slide a .img {
    height: 367px;
  }
  .indexVideo .videoBox .swiper-slide a .img .iconfont {
    bottom: 20px;
    right: 20px;
    font-size: 34px;
    line-height: 38px;
  }
  .indexVideo .videoBox .swiper-slide a .cont {
    padding-top: 50px;
  }
  .indexVideo .videoBox .swiper-slide a .cont .label {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
  }
  .indexVideo .videoBox .swiper-slide a .cont .tit {
    font-size: 18px;
    line-height: 22px;
  }
  .indexVideo .videoBox .swiper-slide a .cont .txt {
    font-size: 14px;
    line-height: 18px;
    margin-top: 20px;
  }
  .indexVideo .videoBox .arrowBox .arrow {
    width: 120px;
    height: 40px;
  }
  .indexVideo .videoBox .arrowBox .arrow .swiper-button-next,
  .indexVideo .videoBox .arrowBox .arrow .swiper-button-prev {
    width: 40px;
    height: 40px;
  }
  .indexVideo .videoBox .arrowBox .arrow .swiper-button-next::after,
  .indexVideo .videoBox .arrowBox .arrow .swiper-button-prev::after {
    font-size: 12px;
  }
}
/* indexGuest */
.indexGuest {
  width: 100%;
  padding: 80px 0;
  background: url(../images/guestBg.jpg) center center no-repeat;
  background-size: cover;
}

.indexGuest .guestWall {
  margin-top: 50px;
}
.indexGuest .indexTitle .title{
  width: 100%;
  text-align: center;
  font-size: 36px;
  font-weight: bold;
  line-height: 40px;
  color: #3D3D3D;
}
.indexGuest .guestGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;
  gap: 30px;
  margin: 0 auto;
}

.indexGuest .guestCard {
  display: flex;
  align-items: center;
  padding: 35px 24px;
  transition: all 0.3s ease;
  border: 2px solid rgba(8, 44, 164, 0.08);
}

.indexGuest .guestCard:hover {
  /* transform: translateY(-5px); */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  background: linear-gradient(180deg, #A1B7FF -107%, #FFFFFF 113%);
}

.indexGuest .guestAvatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  flex-shrink: 0;
}

.indexGuest .guestAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.indexGuest .guestInfo {
  flex: 1;
}

.indexGuest .guestName {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
  line-height: 1.2;
}

.indexGuest .guestCompany {
  font-size: 16px;
  color: #999;
  margin-bottom: 4px;
  line-height: 1.4;
}



/* 响应式设计 */
@media (max-width: 1680px) {
  .indexGuest {
    padding: 80px 0;
  }

  .indexGuest .guestWall {
    margin-top: 50px;
  }

  .indexGuest .guestGrid {
    gap: 30px;
    width: 100%;
  }

  .indexGuest .guestCard {
    padding: 20px;
  }

  .indexGuest .guestAvatar {
    width: 70px;
    height: 70px;
    margin-right: 16px;
  }

  .indexGuest .guestName {
    font-size: 16px;
  }
}

@media (max-width: 1480px) {
  .indexGuest {
    padding: 80px 0;
  }

  .indexGuest .guestGrid {
    gap: 30px;
    width: 100%;
  }

  .indexGuest .guestCard {
    padding: 18px;
  }

  .indexGuest .guestAvatar {
    width: 60px;
    height: 60px;
    margin-right: 14px;
  }

  .indexGuest .guestName {
    font-size: 15px;
  }

  .indexGuest .guestCompany,
  .indexGuest .guestTitle {
    font-size: 13px;
  }
}

@media (max-width: 1200px) {
  .indexGuest .guestGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .indexGuest {
    padding: 60px 0;
  }

  .indexGuest .guestGrid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 20px;
  }

  .indexGuest .guestCard {
    padding: 16px;
  }

  .indexGuest .guestAvatar {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .indexGuest .guestName {
    font-size: 14px;
  }

  .indexGuest .guestCompany,
  .indexGuest .guestTitle {
    font-size: 12px;
  }
}

/* indexPartner */
.indexPartner {
  width: 100%;
  background-color: #fff;
  padding: 120px 0 90px;
}
.indexPartner .box {
  width: 100%;
}
.indexPartner .box .tit {
  width: 192px;
  height: 105px;
  background: url(../images/partnerImgBg.png) center center no-repeat;
  background-size: 100% 100%;
  font-size: 16px;
  font-weight: bold;
  line-height: 22px;
  text-align: center;
  color: #333;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 20px 0 10px;
  margin-bottom: 16px;
}
.indexPartner .box ul {
  width: calc(100% - 192px);
}
.indexPartner .box ul li {
  width: 192px;
  height: 105px;
  border: 1px solid rgba(51, 51, 51, 0.2);
  background-color: #fff;
  margin-left: 16px;
  margin-bottom: 16px;
  transition: all 0.6s ease;
}
.indexPartner .box ul li a {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.indexPartner .box ul li img {
  max-width: 100%;
  max-height: 100%;
}
@media (max-width: 1680px) {
  .indexPartner .box .tit {
    width: 162px;
    height: 88px;
    margin-bottom: 11px;
    font-size: 16px;
    line-height: 22px;
    padding: 0 20px 0 10px;
  }
  .indexPartner .box ul {
    width: calc(100% - 162px);
  }
  .indexPartner .box ul li {
    width: 162px;
    height: 88px;
    margin-left: 11px;
    margin-bottom: 11px;
  }
}
@media (max-width: 1480px) {
  .indexPartner .box .tit {
    width: 146px;
    height: 80px;
    margin-bottom: 13px;
    font-size: 14px;
    line-height: 20px;
    padding: 0 20px 0 10px;
  }
  .indexPartner .box ul {
    width: calc(100% - 146px);
  }
  .indexPartner .box ul li {
    width: 146px;
    height: 80px;
    margin-left: 13px;
    margin-bottom: 13px;
  }
}
/* footer */
.footer {
  width: 100%;
  background-color: #262626;
  padding-top: 80px;
}
.footer dl {
  padding-right: 2%;
}
.footer dl dt {
  font-size: 20px;
  line-height: 24px;
  color: #FFFFFF;
  margin-bottom: 20px;
}
.footer dl dd {
  font-size: 16px;
  line-height: 30px;
  color: rgba(255, 255, 255, 0.8);
}
.footer .codeBox .p {
  font-size: 20px;
  line-height: 24px;
  color: #FFFFFF;
  margin-bottom: 20px;
  margin-left: 20px;
}
.footer .codeBox .code {
  width: 100px;
  margin-left: 20px;
}
.footer .codeBox .code img {
  width: 100px;
  display: block;
  background-color: #ffffff;
}
.footer .codeBox .code p {
  font-size: 14px;
  line-height: 18px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-top: 12px;
}
.footer .copyRight {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 60px;
  padding: 30px 0;
  font-size: 14px;
  line-height: 18px;
  text-align: center;
}
.footer .copyRight span {
  color: rgba(255, 255, 255, 0.6);
}
.footer .copyRight a {
  color: rgba(255, 255, 255, 0.6);
}
.footer .copyRight a:hover {
  color: #fff;
}
@media (max-width: 1680px) {
  .footer dl dt {
    font-size: 18px;
    line-height: 22px;
  }
  .footer dl dd {
    font-size: 14px;
    line-height: 28px;
  }
  .footer .codeBox .p {
    font-size: 18px;
    line-height: 22px;
  }
}
/* sidebarBox */
.sidebarBox {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  z-index: 99;
}
.sidebarBox li {
  width: 90px;
  height: 72px;
  background-color: #666;
  border-bottom: 1px solid #fff;
  text-align: center;
  position: relative;
}
.sidebarBox li:last-child {
  border-bottom: none;
}
.sidebarBox li a {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.sidebarBox li a .iconfont {
  color: #fff;
  font-size: 26px;
  line-height: 30px;
}
.sidebarBox li a .t {
  font-size: 14px;
  line-height: 18px;
  color: #fff;
  margin-top: 6px;
}
.sidebarBox li .img {
  width: 100px;
  overflow: hidden;
  background-color: #082CA4;
  padding: 5px;
  position: absolute;
  right: -100px;
  top: 0;
  z-index: 3;
  transition: all 0.6s ease;
}
.sidebarBox li .img img {
  width: 100%;
  display: block;
}
.sidebarBox li:hover {
  background-color: #082CA4;
}
.sidebarBox li:hover .img {
  right: 90px;
}
.sidebarBox.sidebarBox1 li a .iconfont {
  font-size: 24px;
  line-height: 28px;
}
.sidebarBox.sidebarBox1 li a .t {
  font-size: 12px;
  line-height: 16px;
  margin-top: 0;
}
@media (max-width: 1680px) {
  .sidebarBox li {
    width: 80px;
    height: 62px;
  }
  .sidebarBox li a .iconfont {
    font-size: 22px;
    line-height: 26px;
  }
  .sidebarBox li a .t {
    font-size: 12px;
    line-height: 16px;
    margin-top: 6px;
  }
  .sidebarBox li .img {
    width: 90px;
  }
  .sidebarBox li:hover .img {
    right: 80px;
  }
  .sidebarBox.sidebarBox1 li a .iconfont {
    font-size: 20px;
    line-height: 24px;
  }
  .sidebarBox.sidebarBox1 li a .t {
    font-size: 12px;
    line-height: 16px;
    margin-top: 0;
  }
}
/* pageBanner */
.pageBanner {
  width: 100%;
  height: 400px;
  position: relative;
  background-size: cover !important;
}
.pageBanner .box {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}
.pageBanner .box .wrap {
  min-height: 68px;
  align-items: center;
}
.pageBanner .box .location {
  width: 25%;
  align-items: center;
  line-height: 24px;
}
.pageBanner .box .location .sel {
  color: #082CA4;
  font-size: 22px;
  margin-right: 8px;
}
.pageBanner .box .location .icon-jiantou {
  color: #999999;
  font-size: 12px;
  margin: 0 6px;
}
.pageBanner .box .location p {
  font-size: 16px;
  color: #999999;
}
.pageBanner .box .location a {
  font-size: 16px;
  color: #999999;
}
.pageBanner .box .location a:hover,
.pageBanner .box .location a.on {
  color: #082CA4;
}
.pageBanner .box .tabBox {
  width: 75%;
}
.pageBanner .box .tabBox li {
  margin-left: 6%;
  border-bottom: 3px solid #fff;
  position: relative;
}
.pageBanner .box .tabBox li a {
  display: block;
  font-size: 18px;
  line-height: 22px;
  color: #333333;
  padding: 24px 0 23px;
}
.pageBanner .box .tabBox li.on {
  border-bottom: 3px solid #082CA4;
}
.pageBanner .box .tabBox li.on a {
  color: #082CA4;
  font-weight: bold;
}
.pageBanner .box .tabBox li .ul {
  width: 200px;
  position: absolute;
  top: 72px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #FAFBFF;
  padding: 10px;
  display: none;
}
.pageBanner .box .tabBox li .ul.ul1 {
  top: 93px;
}
.pageBanner .box .tabBox li .ul .li {
  width: 100%;
  display: block;
  font-size: 16px;
  line-height: 20px;
  color: #666;
  padding: 12px 0;
  text-align: center;
  font-weight: normal;
}
.pageBanner .box .tabBox li .ul .li:hover {
  color: #082CA4;
}
.pageBanner .box .tabBox.tabBox1 {
  width: 100%;
  flex-wrap: nowrap;
}
.pageBanner .box .tabBox.tabBox1 li {
  margin-left: 0;
  margin-right: 3%;
}
.pageBanner .box .tabBox.tabBox1 li:last-child {
  margin-right: 0;
}
@media (max-width: 1680px) {
  .pageBanner .box .tabBox li {
    margin-left: 5%;
  }
  .pageBanner .box .tabBox li a {
    font-size: 16px;
    line-height: 20px;
  }
  .pageBanner .box .tabBox li .ul {
    top: 70px;
  }
  .pageBanner .box .tabBox li .ul.ul1 {
    top: 91px;
  }
  .pageBanner .box .tabBox li .ul .li {
    font-size: 14px;
    line-height: 18px;
  }
  .pageBanner .box .tabBox.tabBox1 li {
    margin-left: 0;
    margin-right: 3%;
  }
}
@media (max-width: 1480px) {
  .pageBanner .box .location .sel {
    font-size: 18px;
    margin-right: 6px;
  }
  .pageBanner .box .location .icon-jiantou {
    font-size: 10px;
    margin: 0 5px;
  }
  .pageBanner .box .location p {
    font-size: 14px;
  }
  .pageBanner .box .location a {
    font-size: 14px;
  }
  .pageBanner .box .tabBox li a {
    font-size: 14px;
    line-height: 18px;
  }
  .pageBanner .box .tabBox li .ul {
    top: 68px;
  }
  .pageBanner .box .tabBox li .ul.ul1 {
    top: 86px;
  }
  .pageBanner .box .tabBox.tabBox1 li {
    margin-left: 0;
    margin-right: 3%;
  }
}
/* pageTitle */
.pageTitle {
  font-size: 40px;
  line-height: 44px;
  color: #333333;
  position: relative;
  padding-bottom: 24px;
  font-weight: bold;
}
.pageTitle::after {
  content: '';
  width: 40px;
  height: 6px;
  background-color: #082CA4;
  position: absolute;
  bottom: 0;
  left: 0;
}
@media (max-width: 1680px) {
  .pageTitle {
    font-size: 32px;
    line-height: 36px;
  }
  .pageTitle::after {
    width: 32px;
    height: 5px;
  }
}
@media (max-width: 1480px) {
  .pageTitle {
    font-size: 30px;
    line-height: 34px;
    padding-bottom: 20px;
  }
  .pageTitle::after {
    width: 30px;
    height: 4px;
  }
}
/* pageTab */
.pageTab {
  width: 100%;
  padding-top: 50px;
}
.pageTab ul li {
  min-width: 14.6666%;
  height: 56px;
  border-radius: 28px;
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin: 0 1%;
  transition: all 0.6s ease;
}
.pageTab ul li a {
  display: block;
  width: 100%;
  height: 100%;
  font-size: 18px;
  line-height: 56px;
  text-align: center;
  color: #333333;
  padding: 0 10px;
  transition: all 0.6s ease;
}
.pageTab ul li.on,
.pageTab ul li:hover {
  background-color: #082CA4;
}
.pageTab ul li.on a,
.pageTab ul li:hover a {
  color: #fff;
}
@media (max-width: 1680px) {
  .pageTab ul li {
    height: 52px;
    border-radius: 26px;
  }
  .pageTab ul li a {
    font-size: 16px;
    line-height: 52px;
  }
}
@media (max-width: 1480px) {
  .pageTab ul li {
    height: 46px;
    border-radius: 23px;
  }
  .pageTab ul li a {
    font-size: 14px;
    line-height: 46px;
  }
}
/* pageAbout */
.pageAbout {
  width: 100%;
  padding: 80px 0 120px;
}
.pageAbout .text {
  font-size: 16px;
  line-height: 36px;
  color: #666666;
  text-align: justify;
  margin-top: 40px;
}
.pageAbout .text img {
  max-width: 100%;
  height: auto;
  margin: auto;
}
.pageAbout .img {
  width: 100%;
  overflow: hidden;
  margin-top: 30px;
}
.pageAbout .img img {
  width: 100%;
  display: block;
}
/* pageOrganization */
.pageOrganization {
  width: 100%;
  padding: 80px 0 120px;
}
.pageOrganization ul {
  width: 100%;
  margin-top: 40px;
}
.pageOrganization ul li {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-left: 3px solid #082CA4;
  margin-bottom: 30px;
  padding: 30px 40px;
}
.pageOrganization ul li .t1 {
  font-size: 20px;
  line-height: 30px;
  color: #666666;
  font-weight: bold;
}
.pageOrganization ul li .t2 {
  font-size: 16px;
  line-height: 28px;
  color: #666666;
  margin-top: 10px;
}
.pageOrganization ul li .t2 p {
  margin-right: 20px;
}
/* pageNews */
.pageNews {
  width: 100%;
  padding: 80px 0 60px;
}
.pageNews .newsBox {
  width: 100%;
  padding-bottom: 70px;
}
.pageNews .newsBox .swiper-slide {
  background-color: #FAFBFF;
}
.pageNews .newsBox a {
  width: 100%;
  align-items: center;
}
.pageNews .newsBox a .img {
  width: 50%;
  overflow: hidden;
}
.pageNews .newsBox a .img img {
  width: 100%;
  display: block;
  transition: all 0.6s ease;
}
.pageNews .newsBox a .cont {
  width: 50%;
  padding: 0 3.5%;
}
.pageNews .newsBox a .cont .tit {
  font-size: 20px;
  line-height: 36px;
  color: #333;
  transition: all 0.6s ease;
}
.pageNews .newsBox a .cont .txt {
  font-size: 16px;
  line-height: 30px;
  color: #999;
  margin-top: 30px;
}
.pageNews .newsBox a .cont .time {
  font-size: 16px;
  line-height: 30px;
  color: #666;
  margin-top: 60px;
}
.pageNews .newsBox a:hover .img img {
  transform: scale(1.1);
}
.pageNews .newsBox a:hover .cont .tit {
  color: #082CA4;
}
.pageNews .newsBox .swiper-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 0 !important;
}
.pageNews .newsBox .swiper-pagination .swiper-pagination-bullet {
  width: 14px;
  height: 14px;
  opacity: 0.5;
  background-color: #082CA4;
  margin: 0 10px;
}
.pageNews .newsBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 18px;
  height: 18px;
  opacity: 1;
}
.pageNews .lists {
  width: 100%;
  margin: 80px 0 60px;
}
.pageNews .lists li {
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.pageNews .lists li:last-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.pageNews .lists li a {
  background-color: #ffffff;
  padding: 80px 0;
  align-items: center;
}
.pageNews .lists li .time {
  font-size: 22px;
  line-height: 44px;
  color: #999999;
  transition: all 0.6s ease;
}
.pageNews .lists li .time p {
  font-size: 48px;
  color: #333333;
  transition: all 0.6s ease;
}
.pageNews .lists li .img {
  width: 320px;
  overflow: hidden;
}
.pageNews .lists li .img img {
  width: 100%;
  display: block;
  transition: all 0.6s ease;
}
.pageNews .lists li .cont {
  width: calc(100% - 200px);
}
.pageNews .lists li .cont.cont1 {
  width: calc(100% - 370px);
}
.pageNews .lists li .cont .tit {
  font-size: 20px;
  line-height: 30px;
  color: #333333;
  transition: all 0.6s ease;
}
.pageNews .lists li .cont .txt {
  font-size: 16px;
  line-height: 30px;
  color: #999999;
  margin-top: 20px;
}
.pageNews .lists li .cont .time1 {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 20px;
}
.pageNews .lists li .cont .a {
  align-items: center;
  line-height: 20px;
  margin-top: 30px;
}
.pageNews .lists li .cont .a p {
  font-size: 16px;
  color: #999999;
}
.pageNews .lists li .cont .a .iconfont {
  color: #082CA4;
  font-size: 20px;
  transform: rotate(180deg);
  margin-left: 10px;
}
.pageNews .lists li:hover .time {
  color: #082CA4;
}
.pageNews .lists li:hover .time p {
  color: #082CA4;
}
.pageNews .lists li:hover .img img {
  transform: scale(1.1);
}
.pageNews .lists li:hover .cont .tit {
  color: #082CA4;
}
.pageNews .release {
  width: 100%;
  position: relative;
}
.pageNews .release::after {
  content: '';
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
.pageNews .release li {
  width: 46%;
}
.pageNews .release li:nth-child(2n-1) {
  margin-right: 8%;
}
.pageNews .release li .img {
  width: 100%;
  overflow: hidden;
}
.pageNews .release li .img img {
  width: 100%;
  display: block;
  transition: all 0.6s ease;
}
.pageNews .release li .cont {
  width: 100%;
  padding: 30px 20px;
}
.pageNews .release li .cont .tit {
  font-size: 20px;
  line-height: 30px;
  color: #333333;
  transition: all 0.6s ease;
}
.pageNews .release li .cont .txt {
  font-size: 16px;
  line-height: 30px;
  color: #999999;
  margin-top: 20px;
}
.pageNews .release li .cont .time {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 20px;
}
.pageNews .release li:hover .img img {
  transform: scale(1.1);
}
.pageNews .release li:hover .cont .tit {
  color: #082CA4;
}
/* pageActivity */
.pageActivity {
  width: 100%;
  padding: 80px 0 60px;
}
.pageActivity .activityBox {
  width: 100%;
  position: relative;
}
.pageActivity .activityBox .gallery-top {
  width: calc(100% - 250px);
  margin: 0;
}
.pageActivity .activityBox .gallery-top a {
  width: 100%;
}
.pageActivity .activityBox .gallery-top a .img {
  width: 700px;
  height: 488px;
  overflow: hidden;
}
.pageActivity .activityBox .gallery-top a .img img {
  width: 100%;
  height: 100%;
  display: block;
  transition: all 0.6s ease;
}
.pageActivity .activityBox .gallery-top a .cont {
  width: calc(100% - 700px);
  padding: 30px 0 0 30px;
}
.pageActivity .activityBox .gallery-top a .cont .label {
  display: inline-block;
  height: 40px;
  border-radius: 3px;
  background-color: #082CA4;
  font-size: 16px;
  line-height: 40px;
  color: #FFFFFF;
  text-align: center;
  padding: 0 10px;
}
.pageActivity .activityBox .gallery-top a .cont .tit {
  font-size: 20px;
  line-height: 32px;
  color: #333333;
  margin-top: 30px;
  transition: all 0.6s ease;
}
.pageActivity .activityBox .gallery-top a .cont .txt {
  font-size: 16px;
  line-height: 30px;
  color: #666;
  margin-top: 50px;
}
.pageActivity .activityBox .gallery-top a .cont .time {
  margin-top: 50px;
}
.pageActivity .activityBox .gallery-top a .cont .time .p {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 20px;
}
.pageActivity .activityBox .gallery-top a .cont .time .p .iconfont {
  font-size: 20px;
  margin-right: 10px;
}
.pageActivity .activityBox .gallery-top a:hover .img img {
  transform: scale(1.1);
}
.pageActivity .activityBox .gallery-top a:hover .cont .tit {
  color: #082CA4;
}
.pageActivity .activityBox .gallery-top .arrow {
  width: 148px;
  height: 48px;
  position: absolute;
  bottom: 30px;
  left: 730px;
}
.pageActivity .activityBox .gallery-top .arrow .swiper-button-next {
  right: 0;
}
.pageActivity .activityBox .gallery-top .arrow .swiper-button-prev {
  left: 0;
}
.pageActivity .activityBox .gallery-top .arrow .swiper-button-next,
.pageActivity .activityBox .gallery-top .arrow .swiper-button-prev {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #252525;
  margin: 0;
  top: 0;
  transition: all 0.6s ease;
}
.pageActivity .activityBox .gallery-top .arrow .swiper-button-next::after,
.pageActivity .activityBox .gallery-top .arrow .swiper-button-prev::after {
  color: #fff;
  font-size: 14px;
}
.pageActivity .activityBox .gallery-top .arrow .swiper-button-next:hover,
.pageActivity .activityBox .gallery-top .arrow .swiper-button-prev:hover {
  background-color: #082CA4;
}
.pageActivity .activityBox .gallery-thumbs {
  width: 216px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
}
.pageActivity .activityBox .gallery-thumbs .swiper-wrapper {
  justify-content: space-between;
  transform: none !important;
}
.pageActivity .activityBox .gallery-thumbs .swiper-wrapper .swiper-slide {
  height: auto !important;
  background-color: #000;
  cursor: pointer;
}
.pageActivity .activityBox .gallery-thumbs .swiper-wrapper .swiper-slide img {
  width: 100%;
  height: 150px;
  display: block;
  opacity: 0.6;
  transition: all 0.6s ease;
}
.pageActivity .activityBox .gallery-thumbs .swiper-wrapper .swiper-slide.swiper-slide-thumb-active img {
  opacity: 1;
}
.pageActivity .lists {
  width: 100%;
  margin: 80px 0 30px;
}
.pageActivity .lists li {
  width: 32%;
  margin: 0 2% 2% 0;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.6s ease;
}
.pageActivity .lists li:nth-child(3n) {
  margin: 0 0 2% 0;
}
.pageActivity .lists li .img {
  width: 100%;
  height: 320px;
  overflow: hidden;
  position: relative;
}
.pageActivity .lists li .img img {
  width: 100%;
  height: 100%;
  display: block;
}
.pageActivity .lists li .img .label {
  height: 40px;
  background-color: #082CA4;
  font-size: 14px;
  line-height: 40px;
  color: #FFFFFF;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 12px;
}
.pageActivity .lists li .cont {
  width: 100%;
  padding: 6%;
  border-top: none;
}
.pageActivity .lists li .cont .tit {
  font-size: 20px;
  line-height: 32px;
  color: #333333;
  transition: all 0.6s ease;
}
.pageActivity .lists li .cont .txt {
  font-size: 16px;
  line-height: 26px;
  color: #666;
  margin-top: 20px;
  transition: all 0.6s ease;
}
.pageActivity .lists li .cont .time {
  margin-top: 20px;
}
.pageActivity .lists li .cont .time .p {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 20px;
  transition: all 0.6s ease;
}
.pageActivity .lists li .cont .time .p .iconfont {
  font-size: 20px;
  margin-right: 10px;
}
.pageActivity .lists li:hover {
  background-color: #082CA4;
}
.pageActivity .lists li:hover .cont .tit,
.pageActivity .lists li:hover .cont .txt {
  color: #fff;
}
.pageActivity .lists li:hover .cont .time .p {
  color: #fff;
}
@media (max-width: 1680px) {
  .pageActivity .activityBox .gallery-top {
    width: calc(100% - 210px);
  }
  .pageActivity .activityBox .gallery-top a .img {
    width: 580px;
    height: 405px;
  }
  .pageActivity .activityBox .gallery-top a .cont {
    width: calc(100% - 580px);
  }
  .pageActivity .activityBox .gallery-top a .cont .txt {
    margin-top: 30px;
  }
  .pageActivity .activityBox .gallery-top a .cont .time {
    margin-top: 30px;
  }
  .pageActivity .activityBox .gallery-top .arrow {
    left: 610px;
  }
  .pageActivity .activityBox .gallery-thumbs {
    width: 180px;
  }
  .pageActivity .activityBox .gallery-thumbs .swiper-wrapper .swiper-slide img {
    height: 125px;
  }
  .pageActivity .lists {
    margin: 60px 0 30px;
  }
  .pageActivity .lists li .img {
    height: 266px;
  }
}
@media (max-width: 1480px) {
  .pageActivity .activityBox .gallery-top {
    width: calc(100% - 192px);
  }
  .pageActivity .activityBox .gallery-top a .img {
    width: 530px;
    height: 370px;
  }
  .pageActivity .activityBox .gallery-top a .cont {
    width: calc(100% - 530px);
  }
  .pageActivity .activityBox .gallery-top a .cont .label {
    height: 32px;
    font-size: 14px;
    line-height: 32px;
  }
  .pageActivity .activityBox .gallery-top a .cont .tit {
    font-size: 18px;
    line-height: 30px;
  }
  .pageActivity .activityBox .gallery-top a .cont .txt {
    font-size: 14px;
    line-height: 28px;
  }
  .pageActivity .activityBox .gallery-top a .cont .time .p {
    font-size: 14px;
    line-height: 18px;
    margin-top: 15px;
  }
  .pageActivity .activityBox .gallery-top a .cont .time .p .iconfont {
    font-size: 16px;
    margin-right: 6px;
  }
  .pageActivity .activityBox .gallery-top .arrow {
    width: 120px;
    height: 40px;
    left: 560px;
  }
  .pageActivity .activityBox .gallery-top .arrow .swiper-button-next,
  .pageActivity .activityBox .gallery-top .arrow .swiper-button-prev {
    width: 40px;
    height: 40px;
  }
  .pageActivity .activityBox .gallery-thumbs {
    width: 162px;
  }
  .pageActivity .activityBox .gallery-thumbs .swiper-wrapper .swiper-slide img {
    height: 113px;
  }
  .pageActivity .lists li .img {
    height: 244px;
  }
  .pageActivity .lists li .img .label {
    height: 36px;
    line-height: 36px;
  }
  .pageActivity .lists li .cont .tit {
    font-size: 16px;
    line-height: 28px;
  }
  .pageActivity .lists li .cont .txt {
    font-size: 14px;
    line-height: 28px;
  }
  .pageActivity .lists li .cont .time .p {
    font-size: 14px;
    line-height: 18px;
    margin-top: 15px;
  }
  .pageActivity .lists li .cont .time .p .iconfont {
    font-size: 16px;
    margin-right: 6px;
  }
}
/* pageVideo */
.pageVideo {
  width: 100%;
  padding: 80px 0 60px;
}
.pageVideo .videoBox {
  width: 100%;
  position: relative;
}
.pageVideo .videoBox .gallery-top {
  width: 58%;
  margin: 0;
}
.pageVideo .videoBox .gallery-top .img {
  width: 100%;
  height: 584px;
  display: block;
  overflow: hidden;
  background-color: #000;
  position: relative;
}
.pageVideo .videoBox .gallery-top .img img {
  width: 100%;
  height: 100%;
  display: block;
  opacity: 0.6;
}
.pageVideo .videoBox .gallery-top .img .iconfont {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 70px;
}
.pageVideo .videoBox .gallery-thumbs {
  width: 42%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background-color: #FAFBFF;
  overflow: visible;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper {
  justify-content: space-between;
  transform: none !important;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide {
  height: 33.3333% !important;
  font-size: 20px;
  line-height: 38px;
  color: #333333;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(8, 44, 164, 0.2);
  cursor: pointer;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide::after {
  content: '';
  width: calc(100% + 30px);
  height: 100%;
  background-color: #082CA4;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  opacity: 0;
  transition: all 0.6s ease;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide:last-child {
  border-bottom: none;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide p {
  width: 100%;
  position: relative;
  z-index: 2;
  padding: 0 5%;
  transition: all 0.6s ease;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide .iconfont {
  position: relative;
  z-index: 2;
  color: #fff;
  font-size: 32px;
  position: absolute;
  bottom: 5%;
  right: 5%;
  opacity: 0;
  transition: all 0.6s ease;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide.swiper-slide-thumb-active {
  color: #fff;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide.swiper-slide-thumb-active p {
  padding: 0 5% 0 0;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide.swiper-slide-thumb-active::after {
  opacity: 1;
}
.pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide.swiper-slide-thumb-active .iconfont {
  opacity: 1;
}
.pageVideo .lists {
  width: 100%;
  margin: 80px 0 30px;
}
.pageVideo .lists li {
  width: 48.5%;
  margin: 0 3% 3% 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
.pageVideo .lists li:nth-child(2n) {
  margin: 0 0 3% 0;
}
.pageVideo .lists li a {
  display: block;
  padding-bottom: 40px;
}
.pageVideo .lists li .img {
  width: 100%;
  height: 488px;
  overflow: hidden;
  background-color: #000;
  position: relative;
}
.pageVideo .lists li .img img {
  width: 100%;
  height: 100%;
  display: block;
  opacity: 0.6;
}
.pageVideo .lists li .img .iconfont {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 60px;
}
.pageVideo .lists li .tit {
  font-size: 20px;
  line-height: 30px;
  color: #333333;
  margin-top: 30px;
  transition: all 0.6s ease;
}
.pageVideo .lists li .t {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 20px;
}
.pageVideo .lists li:hover .tit {
  color: #082CA4;
}
@media (max-width: 1680px) {
  .pageVideo .videoBox .gallery-top .img {
    height: 487px;
  }
  .pageVideo .lists li .img {
    height: 407px;
  }
}
@media (max-width: 1480px) {
  .pageVideo .videoBox .gallery-top .img {
    height: 446px;
  }
  .pageVideo .videoBox .gallery-top .img .iconfont {
    font-size: 60px;
  }
  .pageVideo .videoBox .gallery-thumbs .swiper-wrapper .swiper-slide {
    font-size: 18px;
    line-height: 32px;
  }
  .pageVideo .lists {
    margin: 60px 0 30px;
  }
  .pageVideo .lists li a {
    padding-bottom: 30px;
  }
  .pageVideo .lists li .img {
    height: 373px;
  }
  .pageVideo .lists li .img .iconfont {
    font-size: 50px;
  }
  .pageVideo .lists li .tit {
    font-size: 18px;
    line-height: 28px;
    margin-top: 30px;
  }
  .pageVideo .lists li .t {
    font-size: 14px;
    line-height: 18px;
    margin-top: 20px;
  }
}
/* pageNewsD */
.pageNewsD {
  width: 100%;
  padding: 80px 0 120px;
}
.pageNewsD .title {
  text-align: center;
  font-size: 24px;
  line-height: 36px;
  color: #333;
  font-weight: bold;
}
.pageNewsD .date {
  color: #999;
  font-size: 16px;
  text-align: center;
  border-bottom: 1px dashed #ddd;
  padding: 30px 0;
  margin-bottom: 40px;
}
.pageNewsD .date span {
  margin: 0 20px;
}
.pageNewsD .text {
  font-size: 16px;
  line-height: 36px;
  color: #333;
}
.pageNewsD .text img {
  max-width: 100%;
  height: auto;
}
.pageNewsD .text video {
  max-width: 80%;
  margin: auto;
  display: block;
}
.pageNewsD .text iframe {
  width: 767px;
  height: 443px;
  margin: 0 auto;
  display: block;
}
.pageNewsD .box {
  width: 100%;
  border-top: 1px dashed #ddd;
  padding: 40px 0 0;
  margin-top: 40px;
}
.pageNewsD .box a {
  color: #999;
  font-size: 16px;
  line-height: 26px;
  display: inline-block;
  margin-bottom: 20px;
  transition: all 0.6s ease;
}
.pageNewsD .box a:hover {
  color: #082CA4;
}
/* pageForum */
.pageForum {
  width: 100%;
  padding: 50px 0 100px;
  margin: 0;
}
.pageForum .Box.on {
  background: linear-gradient(180deg, #082CA4 0%, #082CA4 100%);
}
/* pageCharacter */
.pageCharacter {
  width: 100%;
  padding: 80px 0 60px;
}
.pageCharacter .lists {
  width: 100%;
  margin-bottom: 30px;
}
.pageCharacter .lists li {
  width: 48.5%;
  margin: 0 3% 3% 0;
  border: 1px solid rgba(0, 0, 0, 0.12);
}
.pageCharacter .lists li:nth-child(2n) {
  margin: 0 0 3% 0;
}
.pageCharacter .lists li a {
  align-items: center;
}
.pageCharacter .lists li .img {
  width: 40%;
  overflow: hidden;
}
.pageCharacter .lists li .img img {
  width: 100%;
  display: block;
}
.pageCharacter .lists li .cont {
  width: 60%;
  padding: 3% 5%;
}
.pageCharacter .lists li .cont .name {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  color: #333;
}
.pageCharacter .lists li .cont .p {
  font-size: 16px;
  line-height: 26px;
  color: #999999;
  margin-top: 14px;
}
.pageCharacter .lists li .cont .a {
  font-size: 16px;
  line-height: 22px;
  color: #333333;
  align-items: center;
  margin-top: 50px;
}
.pageCharacter .lists li .cont .a .iconfont {
  font-size: 20px;
  color: #082CA4;
  transform: rotate(180deg);
  margin-left: 8px;
}
/* pageCharacterD */
.pageCharacterD {
  width: 100%;
  padding: 80px 0;
}
.pageCharacterD .gallery-top {
  width: 100%;
}
.pageCharacterD .gallery-top .between {
  width: 100%;
  background-color: #FAFBFF;
}
.pageCharacterD .gallery-top .between .img {
  width: 400px;
  overflow: hidden;
}
.pageCharacterD .gallery-top .between .img img {
  width: 100%;
  display: block;
}
.pageCharacterD .gallery-top .between .cont {
  width: calc(100% - 400px);
  padding: 80px 40px 0 40px;
}
.pageCharacterD .gallery-top .between .cont .name {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  color: #333333;
}
.pageCharacterD .gallery-top .between .cont .t {
  font-size: 16px;
  line-height: 26px;
  color: #999999;
  margin-top: 20px;
}
.pageCharacterD .gallery-top .arrow {
  width: 138px;
  height: 48px;
  position: absolute;
  bottom: 40px;
  left: 440px;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next {
  right: 0;
}
.pageCharacterD .gallery-top .arrow .swiper-button-prev {
  left: 0;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next,
.pageCharacterD .gallery-top .arrow .swiper-button-prev {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #252525;
  margin: 0;
  top: 0;
  transition: all 0.6s ease;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next::after,
.pageCharacterD .gallery-top .arrow .swiper-button-prev::after {
  color: #fff;
  font-size: 14px;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next:hover,
.pageCharacterD .gallery-top .arrow .swiper-button-prev:hover {
  background-color: #082CA4;
}
.pageCharacterD .gallery-thumbs {
  width: 100%;
  margin-top: 40px;
}
.pageCharacterD .gallery-thumbs .swiper-slide {
  background-color: #000;
  cursor: pointer;
}
.pageCharacterD .gallery-thumbs .swiper-slide img {
  width: 100%;
  display: block;
  opacity: 0.6;
  transition: all 0.6s ease;
}
.pageCharacterD .gallery-thumbs .swiper-slide.swiper-slide-thumb-active img {
  opacity: 1;
}
/* pageHotel */
.pageHotel {
  width: 100%;
  padding: 60px 0 120px;
}
.pageHotel .t {
  font-size: 16px;
  line-height: 32px;
  color: #666666;
  margin-top: 20px;
}
.pageHotel .cont {
  align-items: center;
  background-color: #FAFBFF;
  margin-top: 30px;
}
.pageHotel .cont .img {
  width: 50%;
  overflow: hidden;
}
.pageHotel .cont .img img {
  width: 100%;
  display: block;
}
.pageHotel .cont .txt {
  width: 50%;
  font-size: 16px;
  line-height: 28px;
  color: #333333;
  padding: 0 50px;
}
.pageHotel .cont .txt p {
  margin: 10px 0;
}
.pageHotel .t1 {
  font-size: 20px;
  line-height: 24px;
  color: #333333;
  margin-top: 40px;
}
.pageHotel table {
  width: 100%;
  margin-top: 20px;
  text-align: center;
}
.pageHotel table tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.pageHotel table th {
  background-color: #082CA4;
  color: #fff;
  font-weight: normal;
  font-size: 16px;
  line-height: 60px;
  color: #FFFFFF;
}
.pageHotel table td {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 16px;
  line-height: 26px;
  color: #333333;
  padding: 17px 0;
  width: 20%;
}
.pageHotel table td:nth-child(2) {
  width: 25%;
}
.pageHotel table td:nth-child(4) {
  width: 35%;
  border-right: none;
}
.pageHotel .tips {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 30px;
}
.pageHotel .p {
  width: 110px;
  height: 36px;
  line-height: 36px;
  background-color: #082CA4;
  text-align: center;
  font-size: 16px;
  color: #FFFFFF;
  margin-top: 20px;
}
.pageHotel .box {
  width: 1090px;
  height: 120px;
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.1);
  align-items: center;
  padding: 0 40px;
}
.pageHotel .box .t2 {
  width: calc(100% - 160px);
  font-size: 16px;
  line-height: 40px;
  color: #333333;
}
.pageHotel .box a {
  display: block;
  width: 140px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  background: #082CA4;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
}
.pageHotel .map {
  width: 100%;
  margin-top: 40px;
  overflow: hidden;
}
.pageHotel .map img {
  width: 100%;
  display: block;
}
/* pageLive */
.pageLive {
  width: 100%;
  padding: 60px 0 120px;
}
.pageLive .lists {
  width: 100%;
  margin-bottom: 30px;
}
.pageLive .lists li {
  width: 48.5%;
  margin: 0 3% 3% 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
.pageLive .lists li:nth-child(2n) {
  margin: 0 0 3% 0;
}
.pageLive .lists li a {
  display: block;
  padding-bottom: 30px;
}
.pageLive .lists li .img {
  width: 100%;
  overflow: hidden;
}
.pageLive .lists li .img img {
  width: 100%;
  display: block;
}
.pageLive .lists li .tit {
  font-size: 20px;
  line-height: 30px;
  color: #333333;
  margin-top: 30px;
  transition: all 0.6s ease;
}
.pageLive .lists li .time {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 20px;
}
.pageLive .lists li:hover .tit {
  color: #082CA4;
}
.pageLive .lists.lists1 li {
  width: 23.5%;
  margin: 0 2% 2% 0;
}
.pageLive .lists.lists1 li:nth-child(2n) {
  margin: 0 2% 2% 0;
}
.pageLive .lists.lists1 li:nth-child(4n) {
  margin: 0 0 2% 0;
}
.pageLive .lists.lists1 li .tit {
  font-size: 16px;
  line-height: 24px;
}
.pageLive .lists.lists1 li .time {
  font-size: 14px;
  line-height: 18px;
}
/* pageReview */
.pageReview {
  width: 100%;
  padding: 60px 0 120px;
}
.pageReview ul {
  width: 100%;
}
.pageReview ul li {
  width: 32%;
  margin: 0 2% 2% 0;
  background-color: #FAFBFF;
}
.pageReview ul li:nth-child(3n) {
  margin: 0 0 2% 0;
}
.pageReview ul li .img {
  width: 100%;
  height: 270px;
  background-color: #000;
  overflow: hidden;
  position: relative;
}
.pageReview ul li .img img {
  width: 100%;
  height: 100%;
  display: block;
  transition: all 0.6s ease;
}
.pageReview ul li .img .btn {
  width: 68px;
  height: 68px;
  border-radius: 50%;
  background-color: #082CA4;
  text-align: center;
  font-size: 14px;
  line-height: 18px;
  color: #FFFFFF;
  padding: 15px 0;
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0);
  z-index: 1;
  transition: all 0.6s ease;
}
.pageReview ul li .img .btn::after {
  content: '';
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background-color: rgba(8, 44, 164, 0.6);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
.pageReview ul li .t {
  width: 100%;
  text-align: center;
  background-color: #FAFBFF;
  padding: 20px 15px;
  font-size: 18px;
  line-height: 22px;
  color: #333333;
}
.pageReview ul li:hover .img img {
  opacity: 0.5;
}
.pageReview ul li:hover .img .btn {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}
@media (max-width: 1680px) {
  .pageReview ul li .img {
    height: 225px;
  }
}
@media (max-width: 1480px) {
  .pageReview ul li .img {
    height: 206px;
  }
  .pageReview ul li .t {
    font-size: 16px;
    line-height: 20px;
  }
}
/* pageTheme */
.pageTheme {
  width: 100%;
  padding: 60px 0 120px;
}
.pageTheme .text {
  font-size: 16px;
  line-height: 36px;
  color: #666666;
  text-align: justify;
}
.pageTheme .text img {
  max-width: 100%;
  height: auto;
  margin: auto;
}
.pageTheme .img {
  width: 100%;
  overflow: hidden;
}
.pageTheme .img img {
  width: 100%;
  display: block;
}
.pageTheme .cont {
  width: 100%;
  background-color: #FAFBFF;
  padding: 40px;
}
.pageTheme .cont .tit {
  font-size: 36px;
  line-height: 40px;
  color: #333333;
}
.pageTheme .cont .txt {
  font-size: 18px;
  line-height: 36px;
  color: #666666;
  margin-top: 20px;
}
/* pageSchedule */
.pageSchedule {
  width: 100%;
  padding: 60px 0 120px;
}
.pageSchedule .title {
  font-size: 30px;
  line-height: 40px;
  color: #333333;
  text-align: center;
}
.pageSchedule .p {
  font-size: 16px;
  line-height: 24px;
  color: #666666;
  text-align: center;
  margin: 20px 0 40px;
}
.pageSchedule .p span {
  margin: 0 6px;
}
.pageSchedule .time {
  width: 100%;
  height: 60px;
  line-height: 60px;
  background-color: #082CA4;
  color: #fff;
  font-size: 16px;
  text-align: center;
}
.pageSchedule .time.time1 {
  background-color: #52A199;
}
.pageSchedule table {
  width: 100%;
  text-align: center;
}
.pageSchedule table tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.pageSchedule table th {
  background-color: #FAFBFF;
  font-size: 16px;
  line-height: 20px;
  font-weight: normal;
  color: #666666;
  padding: 20px 0;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}
.pageSchedule table th:last-child {
  border-right: none;
}
.pageSchedule table td {
  font-size: 16px;
  line-height: 20px;
  color: #333333;
  padding: 20px 0;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  width: 53%;
}
.pageSchedule table td:nth-child(1) {
  width: 20%;
}
.pageSchedule table td:nth-child(3) {
  width: 27%;
  border-right: none;
}
.pageSchedule .tips {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 30px;
}
/* pageGuest */
.pageGuest {
  width: 100%;
  padding: 100px 0 150px;
}
.pageGuest .lists {
  width: 100%;
}
.pageGuest .lists li {
  width: 32%;
  margin: 0 2% 2% 0;
  border: 1px solid rgba(0, 0, 0, 0.12);
}
.pageGuest .lists li:nth-child(3n) {
  margin: 0 0 2% 0;
}
.pageGuest .lists li a {
  padding: 5%;
  align-items: center;
}
.pageGuest .lists li a .img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
}
.pageGuest .lists li a .img img {
  width: 100%;
  display: block;
}
.pageGuest .lists li a .cont {
  width: calc(100% - 170px);
}
.pageGuest .lists li a .cont .name {
  font-size: 20px;
  line-height: 30px;
  color: #3D3D3D;
}
.pageGuest .lists li a .cont .t {
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  margin-top: 10px;
}
@media (max-width: 1680px) {
  .pageGuest .lists li a {
    padding: 4%;
  }
  .pageGuest .lists li a .img {
    width: 120px;
    height: 120px;
  }
  .pageGuest .lists li a .cont {
    width: calc(100% - 140px);
  }
  .pageGuest .lists li a .cont .name {
    font-size: 18px;
    line-height: 28px;
  }
  .pageGuest .lists li a .cont .t {
    font-size: 12px;
    line-height: 18px;
  }
}
/* pageAtlas */
.pageAtlas {
  width: 100%;
  padding: 100px 0 150px;
}
.pageAtlas ul {
  width: 100%;
}
.pageAtlas ul li {
  float: left;
  width: 48.5%;
  height: 466px;
  margin: 0 3% 3% 0;
}
.pageAtlas ul li:nth-child(2n-1) {
  margin: 0 0 3% 0;
}
.pageAtlas ul li:nth-child(1) {
  width: 66.2%;
  height: 636px;
}
.pageAtlas ul li:nth-child(2) {
  width: 30.8%;
  height: 296px;
  margin: 0 0 3% 3%;
}
.pageAtlas ul li:nth-child(3) {
  width: 30.8%;
  height: 296px;
  margin: 0 0 0 3%;
}
.pageAtlas ul li img {
  width: 100%;
  height: 100%;
  display: block;
}
@media (max-width: 1680px) {
  .pageAtlas ul li {
    height: 388px;
  }
  .pageAtlas ul li:nth-child(1) {
    height: 530px;
  }
  .pageAtlas ul li:nth-child(2) {
    height: 246px;
  }
  .pageAtlas ul li:nth-child(3) {
    height: 246px;
  }
}
@media (max-width: 1480px) {
  .pageAtlas ul li {
    height: 356px;
  }
  .pageAtlas ul li:nth-child(1) {
    height: 486px;
  }
  .pageAtlas ul li:nth-child(2) {
    height: 226px;
  }
  .pageAtlas ul li:nth-child(3) {
    height: 226px;
  }
}
/* pageReport */
.pageReport {
  width: 100%;
  padding: 80px 0 120px;
}
.pageReport .lists {
  width: 100%;
}
.pageReport .lists li {
  width: 48.5%;
  margin: 0 3% 3% 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 3%;
}
.pageReport .lists li:nth-child(2n) {
  margin: 0 0 3% 0;
}
.pageReport .lists li .tit {
  font-size: 20px;
  line-height: 30px;
  color: #333333;
  transition: all 0.6s ease;
}
.pageReport .lists li .time {
  font-size: 16px;
  line-height: 20px;
  color: #999999;
  margin-top: 14px;
}
.pageReport .lists li .down {
  display: block;
  width: 140px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  background-color: #082CA4;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
  margin-top: 30px;
}
.pageReport .lists li:hover .tit {
  color: #082CA4;
}
@media (max-width: 1480px) {
  .pageReport .lists li .tit {
    font-size: 18px;
    line-height: 28px;
  }
}
/* pagePrint */
.pagePrint {
  width: 100%;
  padding: 100px 0 200px;
}
.pagePrint ul {
  width: 100%;
}
.pagePrint ul li {
  width: 100%;
  background: url(../images/printBg.jpg) center center no-repeat;
  background-size: cover;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 4% 4.5%;
  align-items: center;
  cursor: pointer;
}
.pagePrint ul li .img {
  width: 240px;
  overflow: hidden;
}
.pagePrint ul li .img img {
  width: 100%;
  display: block;
}
.pagePrint ul li .cont {
  width: 520px;
  margin-left: 50px;
}
.pagePrint ul li .cont .t {
  font-size: 20px;
  line-height: 30px;
  color: #333333;
}
.pagePrint ul li .cont .p {
  font-size: 18px;
  line-height: 22px;
  color: #333333;
  margin-top: 30px;
}
.pagePrint ul li .cont .open {
  display: block;
  width: 160px;
  height: 42px;
  line-height: 42px;
  border-radius: 21px;
  background-color: #082CA4;
  text-align: center;
  font-size: 14px;
  color: #FFFFFF;
  margin-top: 60px;
}
/* pagePartner */
.pagePartner {
  width: 100%;
  padding: 80px 0 120px;
}
.pagePartner .title {
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
  margin-bottom: 30px;
  border-left: 4px solid #082CA4;
  padding-left: 10px;
}
.pagePartner ul {
  width: 100%;
}
.pagePartner ul li {
  width: 18.4%;
  margin: 0 2% 2% 0;
  border: 1px solid rgba(51, 51, 51, 0.2);
  background-color: #fff;
  overflow: hidden;
  transition: all 0.6s ease;
}
.pagePartner ul li:nth-child(5n) {
  margin: 0 0 2% 0;
}
.pagePartner ul li a {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pagePartner ul li img {
  max-width: 100%;
  max-height: 100%;
}
/* popupBox */
.popupBox {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: none;
}
.popupForm {
  width: 500px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  box-sizing: content-box;
  background-color: #fff;
  border-radius: 2px;
  padding: 40px 20px;
  display: none;
}
.popupForm .close {
  width: 30px;
  height: 30px;
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}
.popupForm .close::after {
  content: '';
  width: 30px;
  height: 2px;
  background-color: #999;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  transition: all 0.6s ease;
}
.popupForm .close::before {
  content: '';
  width: 30px;
  height: 2px;
  background-color: #999;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  transition: all 0.6s ease;
}
.popupForm .close:hover::after {
  background-color: #082CA4;
}
.popupForm .close:hover::before {
  background-color: #082CA4;
}
.popupForm .title {
  text-align: center;
  color: #333;
  font-size: 18px;
  font-weight: bold;
  line-height: 22px;
}
.popupForm .tit {
  text-align: center;
  color: #666;
  font-size: 14px;
  line-height: 18px;
  margin: 10px 0 20px;
}
.popupForm .row {
  width: 100%;
  margin-bottom: 20px;
}
.popupForm .row .p {
  color: #666;
  font-size: 14px;
  line-height: 18px;
  font-weight: bold;
}
.popupForm .row .txt {
  width: 100%;
  border: 1px solid #ddd;
  color: #333;
  font-size: 14px;
  padding: 10px 15px;
  line-height: 20px;
  border-radius: 2px;
  margin-top: 6px;
}
.popupForm .ajaxformbtn {
  display: block;
  width: 100%;
  line-height: 50px;
  background-color: #082CA4;
  border-radius: 2px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
}
