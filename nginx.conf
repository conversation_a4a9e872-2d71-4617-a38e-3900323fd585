server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    index index.php index.html index.htm;

    # 处理静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # 处理PHP文件和路由
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # 处理.html后缀的请求
    location ~ \.html$ {
        try_files $uri /index.php$is_args$args;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;

        # 添加更多FastCGI参数
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }

    # 禁止访问敏感目录
    location ~ ^/(application|thinkphp|runtime)/ {
        deny all;
    }
}
