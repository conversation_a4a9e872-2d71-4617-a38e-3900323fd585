<?php
use think\Route;
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

//Route::rule('/:en','en');    // 投诉与建议

//Route::rule('web_cn','web');

//Route::rule('web/cn/index/index','web/index/index');
//Route::rule('m/en/index/index','en/index/index');

//Route::rule('fbas2024','web2024/index/index');

$route_url = $_SERVER['REQUEST_URI'];

if ($route_url) {
	if (strlen($route_url) == substr_count($route_url, '/')) {
		$route_url = '/';
	}
}

//Route::domain('sc.dsger.com', function() use ($route_url) {if(!$route_url || $route_url == '/'){Route::domain('sc.dsger.com', 'web/index/index');}});
//Route::domain('sc.dsger.com', function() use ($route_url) {if($route_url == 'admin'){Route::domain('sc.dsger.com/admin', 'admin/login/login');}});

return [
    '__pattern__' => [
        'name' => '\w+',
    ],
    '[hello]'     => [
        ':id'   => ['index/hello', ['method' => 'get'], ['id' => '\d+']],
        ':name' => ['index/hello', ['method' => 'post']],
    ],

];
