#!/bin/bash

# 浦江论坛 Docker 启动脚本

echo "=== 浦江论坛 Docker 启动脚本 ==="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p runtime/cache
mkdir -p runtime/log
mkdir -p runtime/temp
mkdir -p mysql/init

# 设置权限
echo "🔐 设置目录权限..."
chmod -R 755 runtime
chmod -R 755 public

# 停止现有容器（如果存在）
echo "🛑 停止现有容器..."
docker-compose down

# 构建并启动容器
echo "🚀 构建并启动容器..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查容器状态
echo "📊 检查容器状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "✅ 启动完成！"
echo ""
echo "🌐 访问地址："
echo "   主页: http://localhost"
echo "   论坛日程: http://localhost/web/forum/forum.html"
echo "   新闻详情: http://localhost/web/news/detail/id/513.html"
echo ""
echo "🗄️ 数据库信息："
echo "   主机: localhost:3306"
echo "   数据库: pujiangforum"
echo "   用户名: pujiangforum"
echo "   密码: pujiangforum123"
echo ""
echo "📝 常用命令："
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo "   进入PHP容器: docker exec -it pujiangforum_php sh"
echo "   进入Nginx容器: docker exec -it pujiangforum_nginx sh"
echo ""
