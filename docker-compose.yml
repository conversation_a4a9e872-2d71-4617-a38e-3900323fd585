version: '3.8'

services:
  nginx:
    image: nginx:latest
    container_name: pujiangforum_nginx
    ports:
      - "8000:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./public:/var/www/html/public
      - ./application:/var/www/html/application
      - ./thinkphp:/var/www/html/thinkphp
      - ./runtime:/var/www/html/runtime
      - ./vendor:/var/www/html/vendor
    depends_on:
      - php
    networks:
      - pujiangforum_network
    restart: unless-stopped

  php:
    build:
      context: .
      dockerfile: Dockerfile.php
    container_name: pujiangforum_php
    volumes:
      - ./public:/var/www/html/public
      - ./application:/var/www/html/application
      - ./thinkphp:/var/www/html/thinkphp
      - ./runtime:/var/www/html/runtime
      - ./vendor:/var/www/html/vendor
      - ./composer.json:/var/www/html/composer.json
      - ./composer.lock:/var/www/html/composer.lock
    working_dir: /var/www/html
    networks:
      - pujiangforum_network
    restart: unless-stopped

  # mysql:
  #   image: mysql:8.0
  #   container_name: pujiangforum_mysql
  #   environment:
  #     MYSQL_ROOT_PASSWORD: root123456
  #     MYSQL_DATABASE: pujiangforum
  #     MYSQL_USER: pujiangforum
  #     MYSQL_PASSWORD: pujiangforum123
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./mysql/init:/docker-entrypoint-initdb.d
  #   networks:
  #     - pujiangforum_network
  #   restart: unless-stopped
  #   command: --default-authentication-plugin=mysql_native_password

networks:
  pujiangforum_network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
