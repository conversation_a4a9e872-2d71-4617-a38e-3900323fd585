<?php
use think\Route;
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

//Route::rule('/:en','en');    // 投诉与建议

//Route::rule('web_cn','web');

//Route::rule('web/cn/index/index','web/index/index');
//Route::rule('m/en/index/index','en/index/index');

//Route::rule('fbas2024','web2024/index/index');

$route_url = $_SERVER['REQUEST_URI'];

if ($route_url) {
	if (strlen($route_url) == substr_count($route_url, '/')) {
		$route_url = '/';
	}
}

//Route::domain('sc.dsger.com', function() use ($route_url) {if(!$route_url || $route_url == '/'){Route::domain('sc.dsger.com', 'web/index/index');}});
//Route::domain('sc.dsger.com', function() use ($route_url) {if($route_url == 'admin'){Route::domain('sc.dsger.com/admin', 'admin/login/login');}});

// 新闻相关路由
Route::rule('web/news/detail/id/:id', 'web/news/detail');
Route::rule('web/news/news/pid/:pid', 'web/news/news');
Route::rule('web/news/news/pid/:pid/sort_id/:sort_id', 'web/news/news');

// 论坛相关路由
Route::rule('web/forum/forum', 'web/forum/forum');

// 其他web路由
Route::rule('web/news/news', 'web/news/news');
Route::rule('web/news/release', 'web/news/release');
Route::rule('web/news/activity', 'web/news/activity');
Route::rule('web/news/interview', 'web/news/interview');
Route::rule('web/news/video', 'web/news/video');

// API路由
Route::rule('api/schedule/schedule_zs', 'api/schedule/schedule_zs', 'POST');
Route::rule('api/schedule/schedule', 'api/schedule/schedule', 'POST');
Route::rule('api/schedule/schedule_time', 'api/schedule/schedule_time', 'POST');

return [
    '__pattern__' => [
        'name' => '\w+',
        'id' => '\d+',
        'pid' => '\d+',
        'sort_id' => '\d+',
    ],
    '[hello]'     => [
        ':id'   => ['index/hello', ['method' => 'get'], ['id' => '\d+']],
        ':name' => ['index/hello', ['method' => 'post']],
    ],

];
