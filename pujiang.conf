server {
  server_name localhost;
  listen 8080;
  #ssl_certificate /home/<USER>/cert/escrm.club.pem;
  #ssl_certificate_key /home/<USER>/cert/escrm.club.key;
  #ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
  
  root Users\hhhhhh\projects\lanqiProjects\pujiangforum\public;
  index index.php index.html index.htm;

  client_max_body_size 200m;
  # 移动端中文版路由
  location /mobilecn/ {
      index index.html;
      try_files $uri $uri/ /mobilecn/index.html;
   }

  # 移动端英文版路由
  location /mobileen/ {
      index index.html;
      try_files $uri $uri/ /mobileen/index.html;
  }

  location / {
    try_files $uri $uri/ @rewrite;
  }

  location @rewrite {
      rewrite ^(.*)$ /index.php?s=/$1 last;
  }

  ; location ~ \.php$ {
  ;   try_files $uri =404;
  ;   fastcgi_split_path_info ^(.+\.php)(/.+)$;
  ;   fastcgi_pass 127.0.0.1:9000;
  ;   fastcgi_index index.php;
  ;   fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
  ;   include "D:/soft/nginx-1.29.0/conf/fastcgi_params";
  ;   fastcgi_param PATH_INFO $fastcgi_path_info;
  ;   fastcgi_param HTTPS on;
  ;   fastcgi_read_timeout 600;
  ;   fastcgi_connect_timeout 60;
  ;   fastcgi_send_timeout 600;
  ; }
  
  # 静态文件缓存
  #location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
  #    expires 1d;
  #    add_header Cache-Control "public, immutable";
  #    try_files $uri =404;
  #}
    
   # 禁止访问隐藏文件
  location ~ /\. {
      deny all;
      access_log off;
      log_not_found off;
  }
    
  # 禁止访问敏感文件
  location ~* \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
      deny all;
  }

}
