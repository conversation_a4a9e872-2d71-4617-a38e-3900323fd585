<?php
namespace app\api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Schedule extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240801 **/
	public function _initialize() {

    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 测试     测试     测试     测试     测试     测试     测试     测试     测试     测试     测试     测试     测试
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取日程日期列表 **/
	/** 作者：@20240801 **/
	public function schedule_time() {

		/** 获取参数 **/
		$pageIndex	 = input('page', 1);			//第几页
		$pageSize	 = input('num', 100);			//每页数量
		$auditStatus = input('status', -2);			//状态：-1 审核通过的，-2全部
		$rangeStart	 = input('start_time', '');		//开始时间
		$rangeEnd	 = input('end_time', '');		//结束时间

		$tablePkey = 'FT1I2TCF21OPJVVF';	//测试环境
		//$tablePkey = 'FT1I2VP9E4H26VVU';	//生产环境

		if (!$rangeStart) {
			$rangeStart = '2024-07-30 00:00:00';
		}

		if (!$rangeEnd) {
			$rangeEnd = '2024-07-30 23:59:59';
		}

		/** 封装参数 **/
		$row['tablePkey']	= $tablePkey;
		$row['pageIndex']	= $pageIndex;
		$row['pageSize']	= $pageSize;
		$row['auditStatus'] = $auditStatus;

		$rangeFields[0]['fieldType']	= 'dateTime';
		$rangeFields[0]['fieldCode']	= 'forumtime';
		$rangeFields[0]['rangeStart']	= $rangeStart;
		$rangeFields[0]['rangeEnd']		= $rangeEnd;

		$row['rangeFields'] = $rangeFields;
		
		/** 测试环境 **/
		$url = 'https://test-gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		/** 生产环境 **/
		//$url = 'https://gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json'
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $result]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取日程日期列表（测试环境）     获取日程日期列表（测试环境）     获取日程日期列表（测试环境）
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取日程日期列表（测试环境） **/
	/** 作者：@20240801 **/
	public function schedule() {

		/** 获取参数 **/
		$pageIndex	 = input('page', 1);			//第几页
		$pageSize	 = input('num', 100);			//每页数量
		$auditStatus = input('status', -2);			//状态：-1 审核通过的，-2全部
		$rangeStart	 = input('start_time', '');		//开始时间
		$rangeEnd	 = input('end_time', '');		//结束时间

		$tablePkey = 'FT1I2TCF21OPJVVF';	//测试环境
		//$tablePkey = 'FT1I2VP9E4H26VVU';	//生产环境
		
		if (!$rangeStart) {
			$rangeStart = '2024-07-30 00:00:00';
		}

		if (!$rangeEnd) {
			$rangeEnd = '2024-07-30 23:59:59';
		}

		/** 封装参数 **/
		$row['tablePkey']	= $tablePkey;
		$row['pageIndex']	= $pageIndex;
		$row['pageSize']	= $pageSize;
		$row['auditStatus'] = $auditStatus;

		$rangeFields[0]['fieldType']	= 'dateTime';
		$rangeFields[0]['fieldCode']	= 'forumtime';
		$rangeFields[0]['rangeStart']	= $rangeStart;
		$rangeFields[0]['rangeEnd']		= $rangeEnd;

		$row['rangeFields'] = $rangeFields;
		
		/** 测试环境 **/
		$url = 'https://test-gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		/** 生产环境 **/
		//$url = 'https://gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json'
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		if ($result['businessCode'] != 0) {
			return json_encode(['code' => 1, 'msg' => $result['businessMessage'], 'data' => $result]);
		}

		if (!count($result['returnObj'])) {
			return json_encode(['code' => 1, 'msg' => '没有论坛日程信息', 'data' => $result]);
		}

		/** 单位类别中文 **/
		$dwmc_zh['a'] = '承办单位';
		$dwmc_zh['b'] = '协办单位';
		$dwmc_zh['c'] = '支持单位';

		/** 单位类别英文 **/
		$dwmc_en['a'] = 'Organizer';
		$dwmc_en['b'] = 'Co-Organizer';
		$dwmc_en['c'] = 'Supporter';

		$data = [];
		
		/** 循环数据重新封装 **/
		foreach ($result['returnObj'] as $k => $v) {

			$data[$k]		= [];
			$data[$k]['lt'] = [];	//论坛内容
			$data[$k]['dw'] = [];	//承办单位
			$data[$k]['yc'] = [];	//议程（时段）

			/** 封装论坛内容 **/
			if (count($v['forumContent'])) {
				$data[$k]['lt']['attendeePerResp']		= $v['forumContent']['attendeePerResp'];
				$data[$k]['lt']['auditStatus']			= $v['forumContent']['auditStatus'];
				$data[$k]['lt']['auditStatusName']		= $v['forumContent']['auditStatusName'];
				$data[$k]['lt']['createdDate']			= $v['forumContent']['createdDate'];
				$data[$k]['lt']['formNumber']			= $v['forumContent']['formNumber'];
				$data[$k]['lt']['latestUpdatedDate']	= $v['forumContent']['latestUpdatedDate'];
				$data[$k]['lt']['pkey']					= $v['forumContent']['pkey'];
				$data[$k]['lt']['staging']				= $v['forumContent']['staging'];
				
				/** 封装议程信息 **/
				foreach ($v['forumContent']['contents'] as $kk => $vv) {
					$data[$k]['lt'][$vv['fieldCode']] = $vv['fieldValue'];
				}
			}

			/** 封装承办单位 **/
			if (count($v['unitContent'])) {
				foreach ($v['unitContent'] as $kk => $vv) {
					$data[$k]['dw'][$kk]['attendeePerResp']		= $vv['attendeePerResp'];
					$data[$k]['dw'][$kk]['auditStatus']			= $vv['auditStatus'];
					$data[$k]['dw'][$kk]['auditStatusName']		= $vv['auditStatusName'];
					$data[$k]['dw'][$kk]['createdDate']			= $vv['createdDate'];
					$data[$k]['dw'][$kk]['formNumber']			= $vv['formNumber'];
					$data[$k]['dw'][$kk]['latestUpdatedDate']	= $vv['latestUpdatedDate'];
					$data[$k]['dw'][$kk]['pkey']				= $vv['pkey'];
					$data[$k]['dw'][$kk]['staging']				= $vv['staging'];
					
					/** 封装议程信息 **/
					foreach ($vv['contents'] as $kkk => $vvv) {
						$data[$k]['dw'][$kk][$vvv['fieldCode']] = $vvv['fieldValue'];

						if ($vvv['fieldCode'] == 'unitType') {
							$data[$k]['dw'][$kk]['unitType_zh_name'] = $dwmc_zh[$vvv['fieldValue']];
							$data[$k]['dw'][$kk]['unitType_en_name'] = $dwmc_en[$vvv['fieldValue']];
						}
					}
				}
			}

			/** 封装议程（时段） **/
			if (count($v['agendaContent'])) {
				foreach ($v['agendaContent'] as $kk => $vv) {
					$data[$k]['yc'][$kk]['attendeePerResp']		= $vv['attendeePerResp'];
					$data[$k]['yc'][$kk]['auditStatus']			= $vv['auditStatus'];
					$data[$k]['yc'][$kk]['auditStatusName']		= $vv['auditStatusName'];
					$data[$k]['yc'][$kk]['createdDate']			= $vv['createdDate'];
					$data[$k]['yc'][$kk]['formNumber']			= $vv['formNumber'];
					$data[$k]['yc'][$kk]['latestUpdatedDate']	= $vv['latestUpdatedDate'];
					$data[$k]['yc'][$kk]['pkey']				= $vv['pkey'];
					$data[$k]['yc'][$kk]['staging']				= $vv['staging'];
					
					/** 封装议程信息 **/
					foreach ($vv['contents'] as $kkk => $vvv) {
						$data[$k]['yc'][$kk][$vvv['fieldCode']] = $vvv['fieldValue'];

						if ($vvv['fieldCode'] == 'agendaTime') {
							if (isset($vvv['fieldValue'][0]) && isset($vvv['fieldValue'][1])) {
								$data[$k]['yc'][$kk]['duration_time'] = $vvv['fieldValue'][0] . ',' . $vvv['fieldValue'][1];

								$data[$k]['yc'][$kk]['duration_sd'] = substr($vvv['fieldValue'][0], 11, 5) . ' - ' . substr($vvv['fieldValue'][1], 11, 5);
							} else if (isset($vvv['fieldValue'][0])) {
								$data[$k]['yc'][$kk]['duration_time'] = $vvv['fieldValue'][0];

								$data[$k]['yc'][$kk]['duration_sd'] = substr($vvv['fieldValue'][0], 11, 5);
							} else {
								$data[$k]['yc'][$kk]['duration_time'] = '';

								$data[$k]['yc'][$kk]['duration_sd'] = '';
							}
						}

						/*if ($vvv['fieldCode'] == 'associatedAttendee') {
							if (isset($data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'])) {
								$data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'] = 'https://test-fs.31huiyi.com/' . $data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'];
							}
						}*/
					}
				}
			}
		}
		
		/** 论坛地点中文 **/
		$area_zh['a'] = '东郊宾馆·紫金厅';
		$area_zh['b'] = '东郊宾馆·锦绣厅';
		$area_zh['d'] = '张江科学会堂张江厅';
		$area_zh['e'] = '张江科学会堂张江厅A';
		$area_zh['f'] = '张江科学会堂张江厅B';
		$area_zh['g'] = '张江科学会堂科创厅';
		$area_zh['h'] = '张江科学会堂 海科厅中心舞台';
		$area_zh['i'] = '张江科学会堂203会议室';
		$area_zh['j'] = '张江科学会堂303会议室';
		$area_zh['k'] = '张江科学会堂304会议室';
		$area_zh['l'] = '张江科学会堂305会议室';
		$area_zh['m'] = '张江科学会堂402会议室';
		$area_zh['n'] = '张江科学会堂403会议室';
		$area_zh['o'] = '张江科学会堂404会议室';
		$area_zh['p'] = '张江科学会堂405会议室';
		$area_zh['q'] = '上海远洋宾馆';
		$area_zh['r'] = '大零号湾';
		$area_zh['s'] = '外场';
		$area_zh['t'] = '线上';
		$area_zh['u'] = '国创中心总部';
		$area_zh['v'] = '松江区';

		/** 论坛地点英文 **/
		$area_en['a'] = 'Zijin Hall, Convention Center, Dongjiao Hotel';
		$area_en['b'] = 'Jinxiu Hall, Convention Center, Dongjiao Hotel';
		$area_en['c'] = 'Zhangjiang Hall, 2F, ZhangJiang Science Hall';
		$area_en['d'] = 'Zhangjiang Hall A, 2F, ZhangJiang Science Hall';
		$area_en['e'] = 'Zhangjiang Hall B, 2F, ZhangJiang Science Hall';
		$area_en['f'] = 'Inspire Hall, 2F, ZhangJiang Science Hall';
		$area_en['g'] = 'Haike Hall, 1F, ZhangJiang Science Hall';
		$area_en['h'] = 'Conference Room 203, ZhangJiang Science Hall';
		$area_en['j'] = 'Conference Room 303, ZhangJiang Science Hall';
		$area_en['k'] = 'Conference Room 304, ZhangJiang Science Hall';
		$area_en['l'] = 'Conference Room 305, ZhangJiang Science Hall';
		$area_en['m'] = 'Conference Room 402, ZhangJiang Science Hall';
		$area_en['n'] = 'Conference Room 403, ZhangJiang Science Hall';
		$area_en['o'] = 'Conference Room 404, ZhangJiang Science Hall';
		$area_en['p'] = 'Conference Room 405, ZhangJiang Science Hall';
		$area_en['q'] = 'Ocean Hotel Shanghai';
		$area_en['r'] = 'GREATER NEO BAY';
		$area_en['s'] = 'Off-site';
		$area_en['t'] = 'Online';
		$area_en['u'] = 'NATIONAL INNOVATION CENTER par EXCELLENCE (Shanghai)';
		$area_en['v'] = 'Songjiang District';

		//return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);

		/** 重新封装数据 **/
		foreach ($data as $k => $v) {

			$dw_data = [];	//单位重组

			/** 循环议程（时段） **/
			foreach ($v['dw'] as $kk => $vv) {
				if (isset($vv['unitType']) && $vv['unitType']) {
					if (!isset($dw_data[$vv['unitType']])) {
						$dw_data[$vv['unitType']] = [];
					}

					$dw_data[$vv['unitType']][] = $vv;
				}
			}

			$data[$k]['dw_data'] = $dw_data;
			
			$yc_data = [];	//第一次重组 以时间为键
			$yc_row	 = [];	//第二次重组 自增键值
			
			/** 循环议程（时段） **/
			foreach ($v['yc'] as $kk => $vv) {

				if (!isset($vv['duration_time'])) {
					$vv['duration_time'] = 'cs33';
				}

				/** 以时段为 键 设置数组结构 **/
				if (!isset($yc_data[$vv['duration_time']])) {

					$yc_data[$vv['duration_time']] = [];
					
					/** 循环信息封入新的数组 **/
					foreach ($vv as $kkk => $vvv) {
						if ($kkk != 'associatedAttendee') {
							$yc_data[$vv['duration_time']][$kkk] = $vvv;
						} else {
							$yc_data[$vv['duration_time']][$kkk][] = $vvv;
						}
					}
				} else {
					$yc_data[$vv['duration_time']]['associatedAttendee'][] = $vv['associatedAttendee'];
				}
			}

			foreach ($yc_data as $kk => $vv) {
				$yc_row[] = $vv;
			}
			
			$data[$k]['yc_row'] = $yc_row;

			if (isset($data[$k]['lt']['FC1I3CSLNBVPJVPD']) && isset($area_zh[$data[$k]['lt']['FC1I3CSLNBVPJVPD']])) {
				$data[$k]['lt']['FC1I3CSLNBVPJVPD_name'] = $area_zh[$data[$k]['lt']['FC1I3CSLNBVPJVPD']];
			}

			if (isset($data[$k]['lt']['FC1I3CSLNCVPJVWD']) && isset($area_en[$data[$k]['lt']['FC1I3CSLNCVPJVWD']])) {
				$data[$k]['lt']['FC1I3CSLNCVPJVWD_name'] = $area_en[$data[$k]['lt']['FC1I3CSLNCVPJVWD']];
			}

			unset($data[$k]['yc']);
		}

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取日程日期列表（正式环境）     获取日程日期列表（正式环境）     获取日程日期列表（正式环境）
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取日程日期列表（正式环境） **/
	/** 作者：@20240801 **/
	public function schedule_zs() {

		/** 获取参数 **/
		$pageIndex	 = input('page', 1);			//第几页
		$pageSize	 = input('num', 100);			//每页数量
		$auditStatus = input('status', -2);			//状态：-1 审核通过的，-2全部
		$rangeStart	 = input('start_time', '');		//开始时间
		$rangeEnd	 = input('end_time', '');		//结束时间

		// 返回模拟数据，匹配前端新的数据结构
		return $this->getMockScheduleData($rangeStart, $rangeEnd);
	}

	/** 功能：获取模拟日程数据 **/
	/** 作者：@20250128 **/
	private function getMockScheduleData($rangeStart, $rangeEnd) {

		if (!$rangeStart) {
			$rangeStart = '2025-07-25 00:00:00';
		}

		if (!$rangeEnd) {
			$rangeEnd = '2025-07-25 23:59:59';
		}

		// 解析日期
		$date = date('Y-m-d', strtotime($rangeStart));

		// 根据日期返回不同的数据
		$mockData = [];

		if ($date == '2025-07-25') {
			$mockData = [
				[
					'lt' => [
						'FC1I3KSV08QWDZVDU' => 1,
						'FC1I3KSV02JWDZVEX' => '开幕式暨主旨演讲（Y HUB）韩国科学技术院',
						'FC1I3KSV03VWDZVUO_name' => '主会场',
						'FC1I3KSV03DWDZVUY' => '第十一次科技创新智库国际研讨会将在上海举办，将聚焦"科技创新中心建设"，区域科技创新中心建设，目标任务、上海、粤港澳大湾区三个国际创新中心建设正在加速推进。各地区创新资源配置的新格局，为大科技创新体系人才产业的力量，加强各方合作共赢的科技合作与交流，不断提升自主创新',
						'forumtime' => ['2025-07-25 09:30:00', '2025-07-25 21:00:00'],
						'start_time' => '09:30',
						'end_time' => '21:00',
						'forumtime_type' => 1
					],
					'dw_data' => [
						'a' => [
							['FC1I3KT05B6WDZVDO' => '中华人民共和国科学技术部'],
							['FC1I3KT05B6WDZVDO' => '上海市人民政府']
						],
						'b' => [
							['FC1I3KT05B6WDZVDO' => '科技部国家科学技术委员会'],
							['FC1I3KT05B6WDZVDO' => '同济大学']
						],
						'c' => [
							['FC1I3KT05B6WDZVDO' => '科技部国家科学技术委员会'],
							['FC1I3KT05B6WDZVDO' => '同济大学']
						]
					],
					'yc_row' => [
						[
							'duration_sd' => '09:00-09:30',
							'topicCn' => '开幕致辞',
							'FC1I3KT0NT6WDZVMF' => false,
							'FC1I3KT0NQ3WDZVIB' => 'speaker',
							'associatedAttendee' => [
								[
									'fullName' => '刘冬梅',
									'company' => '中国科学技术发展战略研究院',
									'position' => '党委书记',
									'avatar' => 'default_avatar.png'
								]
							]
						],
						[
							'duration_sd' => '09:30-10:30',
							'topicCn' => '主旨演讲：应对颠覆性创新的公共治理策略',
							'FC1I3KT0NT6WDZVMF' => false,
							'FC1I3KT0NQ3WDZVIB' => 'speaker',
							'associatedAttendee' => [
								[
									'fullName' => '张大力',
									'company' => '中国科学院',
									'position' => '院士',
									'avatar' => 'default_avatar.png'
								]
							]
						]
					]
				],
				[
					'lt' => [
						'FC1I3KSV08QWDZVDU' => 1,
						'FC1I3KSV02JWDZVEX' => '2024浦江创新论坛主论坛《新质生产力科技创新》',
						'FC1I3KSV03VWDZVUO_name' => '主会场',
						'FC1I3KSV03DWDZVUY' => '聚焦新质生产力发展，探讨科技创新与产业转型升级的深度融合。',
						'forumtime' => ['2025-07-25 10:00:00', '2025-07-25 11:30:00'],
						'start_time' => '10:00',
						'end_time' => '11:30',
						'forumtime_type' => 1
					],
					'dw_data' => [
						'a' => [
							['FC1I3KT05B6WDZVDO' => '上海市科学技术委员会']
						],
						'b' => [
							['FC1I3KT05B6WDZVDO' => '浦东新区科技和经济委员会']
						]
					],
					'yc_row' => [
						[
							'duration_sd' => '10:00-10:30',
							'topicCn' => '新质生产力的科技内涵与发展路径',
							'FC1I3KT0NT6WDZVMF' => false,
							'FC1I3KT0NQ3WDZVIB' => 'speaker',
							'associatedAttendee' => [
								[
									'fullName' => '周美玲',
									'company' => '字节跳动',
									'position' => '技术副总裁',
									'avatar' => 'default_avatar.png'
								]
							]
						]
					]
				]
			];
		} else if ($date == '2025-07-26') {
			$mockData = [
				[
					'lt' => [
						'FC1I3KSV08QWDZVDU' => 1,
						'FC1I3KSV02JWDZVEX' => '人工智能与未来科技发展论坛',
						'FC1I3KSV03VWDZVUO_name' => '主会场',
						'FC1I3KSV03DWDZVUY' => '探讨人工智能技术发展趋势，分析AI在各行业的应用前景。',
						'forumtime' => ['2025-07-26 09:00:00', '2025-07-26 17:00:00'],
						'start_time' => '09:00',
						'end_time' => '17:00',
						'forumtime_type' => 1
					],
					'dw_data' => [
						'a' => [
							['FC1I3KT05B6WDZVDO' => '中国人工智能学会'],
							['FC1I3KT05B6WDZVDO' => '上海市科学技术委员会']
						]
					],
					'yc_row' => [
						[
							'duration_sd' => '09:00-10:00',
							'topicCn' => 'AI技术发展现状与趋势',
							'FC1I3KT0NT6WDZVMF' => false,
							'FC1I3KT0NQ3WDZVIB' => 'speaker',
							'associatedAttendee' => [
								[
									'fullName' => '李明华',
									'company' => '上海交通大学',
									'position' => '教授',
									'avatar' => 'default_avatar.png'
								]
							]
						]
					]
				]
			];
		} else if ($date == '2025-07-27') {
			$mockData = [
				[
					'lt' => [
						'FC1I3KSV08QWDZVDU' => 1,
						'FC1I3KSV02JWDZVEX' => '闭幕式暨成果发布',
						'FC1I3KSV03VWDZVUO_name' => '主会场',
						'FC1I3KSV03DWDZVUY' => '总结论坛成果，发布重要科技创新倡议和合作协议。',
						'forumtime' => ['2025-07-27 14:00:00', '2025-07-27 16:00:00'],
						'start_time' => '14:00',
						'end_time' => '16:00',
						'forumtime_type' => 1
					],
					'dw_data' => [
						'a' => [
							['FC1I3KT05B6WDZVDO' => '中华人民共和国科学技术部'],
							['FC1I3KT05B6WDZVDO' => '上海市人民政府']
						]
					],
					'yc_row' => [
						[
							'duration_sd' => '14:00-15:00',
							'topicCn' => '论坛成果总结',
							'FC1I3KT0NT6WDZVMF' => false,
							'FC1I3KT0NQ3WDZVIB' => 'speaker',
							'associatedAttendee' => [
								[
									'fullName' => '王晓燕',
									'company' => '复旦大学',
									'position' => '教授',
									'avatar' => 'default_avatar.png'
								]
							]
						]
					]
				]
			];
		}

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $mockData]);
	}

	/** 功能：获取日程日期列表（原始接口备份） **/
	/** 作者：@20240801 **/
	public function schedule_zs_original() {
		// 这里保留了原始的外部API调用逻辑
		// 如果需要恢复原来的功能，可以将此方法的内容复制到 schedule_zs() 方法中

		/** 获取参数 **/
		$pageIndex	 = input('page', 1);			//第几页
		$pageSize	 = input('num', 100);			//每页数量
		$auditStatus = input('status', -2);			//状态：-1 审核通过的，-2全部
		$rangeStart	 = input('start_time', '');		//开始时间
		$rangeEnd	 = input('end_time', '');		//结束时间

		$tablePkey = 'FT1I196B7RRWBVVEF';	//生产环境

		if (!$rangeStart) {
			$rangeStart = '2024-09-07 00:00:00';
		}

		if (!$rangeEnd) {
			$rangeEnd = '2024-09-07 23:59:59';
		}

		/** 封装参数 **/
		$row['tablePkey']	= $tablePkey;
		$row['pageIndex']	= $pageIndex;
		$row['pageSize']	= $pageSize;
		$row['auditStatus'] = $auditStatus;

		$rangeFields[0]['fieldType']	= 'dateTime';
		$rangeFields[0]['fieldCode']	= 'forumtime';
		$rangeFields[0]['rangeStart']	= $rangeStart;
		$rangeFields[0]['rangeEnd']		= $rangeEnd;

		$row['rangeFields'] = $rangeFields;

		/** 生产环境 **/
		$url = 'https://gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json'
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		}

		curl_close($ch);

		$result = json_decode($result, true);

		if ($result['businessCode'] != 0) {
			return json_encode(['code' => 1, 'msg' => $result['businessMessage'], 'data' => $result]);
		}

		if (!count($result['returnObj'])) {
			return json_encode(['code' => 1, 'msg' => '没有论坛日程信息', 'data' => $result]);
		}

		// 这里应该包含原始的数据处理逻辑...
		// 为了节省空间，这里省略了具体的数据处理代码

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $result]);
	}
}